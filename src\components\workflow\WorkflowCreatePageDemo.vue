<template>
  <div class="demo-container">
    <h2>应用卡片下拉菜单演示</h2>
    <div class="demo-card">
      <div class="card-header">
        <div class="card-icon" style="background: linear-gradient(135deg, #6a6ff7 0%, #6fd0ff 100%);">
          <i class="fas fa-robot"></i>
        </div>
        <div class="card-title">示例应用</div>
        <div class="card-type-tag">工作流</div>
      </div>
      
      <div class="card-description">这是一个演示应用，用于展示鼠标悬停日期时显示下拉菜单的功能。</div>
      
      <div class="card-footer">
        <div class="footer-left">
          <div class="card-author">
            <span>演示用户</span>
          </div>
          <span class="card-privacy">公开</span>
          <span class="card-status">已发布</span>
        </div>
        <div class="card-date-wrapper">
          <span
            v-if="!showDots"
            class="card-date"
            ref="dateRef"
            @mouseenter="showDotsDisplay"
            @mouseleave="hideDotsDisplay"
          >2024-01-15 10:30</span>
          <span
            v-if="showDots"
            class="card-dots"
            @mouseenter="showActionDropdown"
            @mouseleave="hideActionDropdown"
          >•••</span>
          <teleport to="body">
            <div
              v-if="actionDropdownVisible"
              :style="{
                position: 'absolute', 
                top: actionDropdownPos.top + 'px', 
                left: actionDropdownPos.left + 'px', 
                zIndex: 99999
              }"
              @mouseenter="cancelActionHide"
              @mouseleave="hideActionDropdown"
            >
              <AppActionDropdown 
                appId="demo-app-id" 
                @action="handleAppAction" 
              />
            </div>
          </teleport>
        </div>
      </div>
    </div>
    
    <div class="demo-info">
      <p>
        <strong>交互步骤：</strong><br>
        1. 将鼠标悬停在右下角的日期上，日期会变为三个点（•••）<br>
        2. 然后将鼠标移动到三个点上，即可显示下拉菜单
      </p>
      <div v-if="lastAction" class="action-result">
        <strong>最后执行的操作：</strong> {{ lastAction }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue';
import AppActionDropdown from './AppActionDropdown.vue';

// 应用操作下拉菜单相关
const actionDropdownVisible = ref(false);
const actionDropdownPos = ref({ top: 0, left: 0 });
const dateRef = ref<HTMLElement | null>(null);
const lastAction = ref('');
const showDots = ref(false);
let actionHideTimer: any = null;
let dotsHideTimer: any = null;

function showDotsDisplay() {
  if (dotsHideTimer) clearTimeout(dotsHideTimer);
  showDots.value = true;
}

function hideDotsDisplay() {
  dotsHideTimer = setTimeout(() => {
    if (!actionDropdownVisible.value) {
      showDots.value = false;
    }
  }, 100);
}

function showActionDropdown(event: MouseEvent) {
  if (actionHideTimer) clearTimeout(actionHideTimer);
  if (dotsHideTimer) clearTimeout(dotsHideTimer);
  actionDropdownVisible.value = true;

  nextTick(() => {
    const target = event.target as HTMLElement;
    if (target) {
      const rect = target.getBoundingClientRect();
      actionDropdownPos.value = {
        top: rect.bottom + window.scrollY + 6,
        left: rect.left + window.scrollX - 80
      };
    }
  });
}

function hideActionDropdown() {
  actionHideTimer = setTimeout(() => {
    actionDropdownVisible.value = false;
    showDots.value = false;
  }, 120);
}

function cancelActionHide() {
  if (actionHideTimer) clearTimeout(actionHideTimer);
}

function handleAppAction(payload: { action: string; appId: string }) {
  actionDropdownVisible.value = false;
  
  const actionNames: { [key: string]: string } = {
    edit: '编辑信息',
    move: '移动',
    permission: '权限设置',
    export: '导出',
    delete: '删除'
  };
  
  lastAction.value = `${actionNames[payload.action] || payload.action} (应用ID: ${payload.appId})`;
  
  console.log('应用操作:', payload);
}
</script>

<style scoped>
.demo-container {
  padding: 2rem;
  max-width: 600px;
  margin: 0 auto;
  background: linear-gradient(135deg, #1a1a1a, #2a2a2a);
  min-height: 100vh;
  color: #cfd8dc;
}

.demo-container h2 {
  color: #4fc3ff;
  text-align: center;
  margin-bottom: 2rem;
}

.demo-card {
  background: linear-gradient(135deg, #23242a 80%, #18181c 100%);
  border-radius: 1.2rem;
  box-shadow: 0 4px 32px 0 #000a, 0 1.5px 8px 0 #4fc3ff11 inset;
  padding: 1.5rem 1.2rem 1.2rem 1.2rem;
  display: flex;
  flex-direction: column;
  gap: 1.1rem;
  margin-bottom: 2rem;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 0.7rem;
  margin-bottom: 0.2rem;
}

.card-icon {
  width: 35px;
  height: 35px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #f0f3f5;
  box-shadow: 0 2px 12px #4fc3ff22;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.card-title {
  font-size: 0.9rem;
  color: #fff;
  margin-bottom: 0.1rem;
}

.card-type-tag {
  margin-left: auto;
  font-size: 0.7rem;
  color: #4fc3ff;
  background: rgba(79, 195, 255, 0.10);
  font-weight: 400;
  padding: 0.18rem 0.7rem;
  border-radius: 0.6rem;
  letter-spacing: 0.01em;
  align-self: center;
  box-shadow: 0 1px 6px #4fc3ff11;
}

.card-description {
  font-size: 0.75rem;
  color: #b0b8c1;
  margin-bottom: 0.8rem;
  flex-grow: 1;
  text-align: left;
  padding-left: 10px;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.98rem;
  color: #b0b8c1;
  margin-top: auto;
  border-top: none;
  padding-top: 0.2rem;
}

.footer-left {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.card-author {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.7rem;
  padding-left: 10px;
}

.card-privacy {
  color: #4fc3ff;
  font-size: 0.7rem;
  margin-left: 15px;
}

.card-status {
  color: #4fc3ff;
  font-size: 0.7rem;
  margin-left: 15px;
}

.card-date-wrapper {
  position: relative;
}

.card-date {
  color: #b0b8c1;
  font-size: 0.7rem;
  margin-right: 10px;
  cursor: pointer;
  padding: 0.2rem 0.4rem;
  border-radius: 0.3rem;
  transition: background 0.2s, color 0.2s;
}

.card-date:hover {
  background: rgba(79, 195, 255, 0.1);
  color: #4fc3ff;
}

.card-dots {
  color: #4fc3ff;
  font-size: 0.9rem;
  margin-right: 10px;
  cursor: pointer;
  padding: 0.2rem 0.4rem;
  border-radius: 0.3rem;
  transition: background 0.2s, color 0.2s;
  background: rgba(79, 195, 255, 0.1);
  font-weight: bold;
  letter-spacing: 0.1em;
}

.card-dots:hover {
  background: rgba(79, 195, 255, 0.2);
  color: #6fd0ff;
}

.demo-info {
  background: rgba(79, 195, 255, 0.05);
  border: 1px solid rgba(79, 195, 255, 0.2);
  border-radius: 0.8rem;
  padding: 1rem;
  text-align: center;
}

.action-result {
  margin-top: 1rem;
  padding: 0.5rem;
  background: rgba(79, 195, 255, 0.1);
  border-radius: 0.5rem;
  color: #4fc3ff;
}
</style>
