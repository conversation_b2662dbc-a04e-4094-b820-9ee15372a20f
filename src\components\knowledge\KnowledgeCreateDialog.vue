<template>
  <div class="dialog-mask">
    <div class="dialog">
      <div class="dialog-header">
        <div class="dialog-icon" :style="{ background: props.kbBg }">
          <i :class="props.kbIcon"></i>
        </div>
        <span>创建一个{{ props.kbTypeName }}</span>
        <i class="fas fa-times close-btn" @click="$emit('close')"></i>
      </div>
      <div class="dialog-body">
        <div class="form-label">取个名字</div>
        <div class="input-row">
          <div class="input-big-icon" :style="{ background: props.kbBg }">
            <i :class="props.kbIcon"></i>
          </div>
          <input 
            class="name-input" 
            :class="{ 'shake': showShake }" 
            placeholder="名称" 
            v-model="name" 
          />
        </div>
        <div v-if="isLoading" class="loading-message">加载中...</div>
        <template v-else>
          <div class="form-group">
            <div class="form-label">索引模型 <i class="fas fa-question-circle"></i></div>
            <select v-model="embeddingModel">
              <option v-for="item in modelOptions.embeddingModels" :key="item" :value="item">
                {{ item }}
              </option>
            </select>
          </div>
          <div class="form-group">
            <div class="form-label">文本理解模型 <i class="fas fa-question-circle"></i></div>
            <select v-model="chatModel">
              <option v-for="item in modelOptions.chatModels" :key="item" :value="item">
                {{ item }}
              </option>
            </select>
          </div>
          <div class="form-group">
            <div class="form-label">图片理解模型 <i class="fas fa-question-circle"></i></div>
            <select v-model="imageModel">
              <option v-for="item in modelOptions.imageModels" :key="item" :value="item">
                {{ item }}
              </option>
            </select>
          </div>
        </template>
      </div>
      <div v-if="error" class="error-message">{{ error }}</div>
      <div class="dialog-footer">
        <button class="cancel-btn" @click="$emit('close')">关闭</button>
        <button class="confirm-btn" @click="handleConfirm" :disabled="isSubmitting">
          {{ isSubmitting ? '创建中...' : '确认创建' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { v4 as uuidv4 } from 'uuid';
import { useKnowledgeStore } from '@/store/knowledge';
import { getModelOptions, createKnowledgeBase } from '@/api/kb';
import { cookieUtils } from '@/utils/cookie';
const router = useRouter();
const knowledgeStore = useKnowledgeStore();
const isLoading = ref(true);
const error = ref<string | null>(null);
const isSubmitting = ref(false);

const props = defineProps<{
  kbTypeId: string
  kbTypeName: string
  kbIcon: string
  kbBg: string
}>();

const modelOptions = ref<{
  chatModels: string[];
  embeddingModels: string[];
  imageModels: string[];
}>({
  chatModels: [],
  embeddingModels: [],
  imageModels: []
});

const name = ref('');
const embeddingModel = ref('');
const chatModel = ref('');
const imageModel = ref('');
const showShake = ref(false);

// 从cookie获取creator
const getCreatorFromCookie = () => {
  let username = '';
  const userCookie = cookieUtils.getCookie('user');
  if (userCookie) {
    try {
      const userObj = JSON.parse(userCookie);
      username = userObj.username;
    } catch (e) {
      username = '';
    }
  }
  return username;
};

onMounted(async () => {
  try {
    isLoading.value = true;
    const options = await getModelOptions();
    modelOptions.value = options;
    // 确保每个模型类型都有默认值（第一个选项）
    if (options.chatModels?.length > 0) {
      chatModel.value = options.chatModels[0];
    }
    if (options.embeddingModels?.length > 0) {
      embeddingModel.value = options.embeddingModels[0];
    }
    if (options.imageModels?.length > 0) {
      imageModel.value = options.imageModels[0];
    }
  } catch (err: unknown) {
    console.error('Failed to fetch model options:', err);
    error.value = '获取模型选项失败，请稍后重试';
  } finally {
    isLoading.value = false;
  }
});

const handleConfirm = async () => {
  if (!name.value) {
    showShake.value = true;
    setTimeout(() => {
      showShake.value = false;
    }, 500);
    return;
  }

  try {
    isSubmitting.value = true;
    // 生成唯一ID
    const kbId = uuidv4();
    
    // 调用API保存知识库信息
    await createKnowledgeBase({
      kbId: kbId,
      kbName: name.value,
      creatorId: getCreatorFromCookie(),
      kbTypeId: props.kbTypeId,
      kbTypeName: props.kbTypeName,
      kbIcon: props.kbIcon,
      kbBg: props.kbBg,
      embeddingModel: embeddingModel.value,
      chatModel: chatModel.value,
      imageModel: imageModel.value,
      kbDesc: `这是一个${props.kbTypeName}知识库。`,
      kbPrivate: 'private',
      accessLevel: 'write'
    });

    // 存到 Pinia
    knowledgeStore.setDatasetInfo({
      kbName: name.value,
      kbTypeId: props.kbTypeId,
      kbTypeName: props.kbTypeName,
      kbIcon: props.kbIcon,
      kbBg: props.kbBg,
      embeddingModel: embeddingModel.value,
      chatModel: chatModel.value,
      imageModel: imageModel.value,
      kbDesc: `这是一个${props.kbTypeName}知识库。`,
      accessLevel: 'write'
    });
    // 跳转
    router.push({ path: '/dataset/detail', query: { kbId: kbId } });
  } catch (err) {
    console.error('Failed to create knowledge base:', err);
    error.value = '创建知识库失败，请稍后重试';
  } finally {
    isSubmitting.value = false;
  }
};
</script>

<style scoped>

.dialog-mask {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.18);
  backdrop-filter: blur(2px);
  -webkit-backdrop-filter: blur(2px);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}
.dialog {
  background: #343539;
  border-radius: 18px;
  width: 480px;
  box-shadow: 0 4px 32px rgba(0,0,0,0.3);
  padding: 0 0 18px 0;
  animation: fadeIn 0.2s;
}
@keyframes fadeIn {
  from { transform: translateY(30px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}
.dialog-header {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1rem;
  font-weight: 500;
  padding: 15px 28px 0 28px;
  color: #e4e6eb;
  position: relative;
  padding-bottom: 10px;
  border-bottom: 1px solid #2c2d30;
}
.dialog-icon {
  color: #fff;
  font-size: 1.5rem;
  border-radius: 10px;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
}
.dialog-icon i {
  font-size: 1rem;
}
.close-btn {
  position: absolute;
  right: 24px;
  top: 24px;
  font-size: 1.2rem;
  color: #8b95a5;
  cursor: pointer;
  transition: color 0.18s;
}
.close-btn:hover {
  color: #5bebf0;
}
.dialog-body {
  padding: 15px 28px 0 28px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.form-group {
  display: flex;
  align-items: center;
  gap: 12px;
  justify-content: space-between;
  padding-bottom: 10px;
}
.form-label {
  color: #e4e6eb;
  font-size: 1rem;
  font-weight: 500;
  white-space: nowrap;
}
.input-row {
  display: flex;
  align-items: center;
  border-radius: 8px;
  padding-bottom: 5px;
  gap: 14px;
  margin-bottom: 8px;
}
.input-big-icon {
  width: 36px;
  height: 36px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 1.5rem;
  flex-shrink: 0;
}
.name-input {
  border: 1.5px solid #2c2d30;
  outline: none;
  font-size: 1rem;
  background: #2c2d30;
  padding: 8px 12px;
  color: #e4e6eb;
  flex: 1;
  border-radius: 8px;
  transition: box-shadow 0.18s, border 0.18s;
}

.name-input:focus {
  border: 1.5px solid #5bebf0;
  background: #2c2d30;
  box-shadow: 0 0 0 2px rgba(91,235,240,0.2);
}
select {
  width: 250px;
  padding: 8px 12px;
  border-radius: 8px;
  border: 1px solid #2c2d30;
  font-size: 1rem;
  background: #2c2d30;
  color: #e4e6eb;
}
select:focus {
  border-color: #5bebf0;
  box-shadow: 0 0 0 2px rgba(91,235,240,0.2);
}
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  padding: 18px 28px 0 28px;
}
.cancel-btn {
  background: #2c2d30;
  color: #e4e6eb;
  border: none;
  border-radius: 8px;
  padding: 8px 24px;
  font-size: 1rem;
  cursor: pointer;
  font-weight: bold;
  transition: background 0.2s;
}
.confirm-btn {
  background: linear-gradient(90deg, #6a6ff7 0%, #7fdcff 100%);
  color: #fbfbfc;
  border: none;
  border-radius: 8px;
  padding: 8px 24px;
  font-size: 1rem;
  cursor: pointer;
  font-weight: bold;
  transition: background 0.2s;
}
.confirm-btn:hover {
  background: linear-gradient(90deg, #878bf1 0%, #53d7f8 100%);
}

/* 下拉框选项样式 */
select option {
  background: #2c2d30;
  color: #e4e6eb;
}

@keyframes shake {
  0%, 100% { border-color: #2c2d30; }
  25% { border-color: #5bebf0; }
  75% { border-color: #5bebf0; }
}

.name-input.shake {
  animation: shake 0.5s;
}

.error-message {
  color: #ff4d4f;
  margin: 10px 28px;
  font-size: 0.9rem;
  text-align: center;
}

.loading-message {
  color: #8b95a5;
  margin: 10px 0;
  font-size: 0.9rem;
}

.confirm-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

</style>
