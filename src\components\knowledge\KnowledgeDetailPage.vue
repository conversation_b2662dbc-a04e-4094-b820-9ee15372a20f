<template>
  <div class="knowledge-detail-layout">
    <!-- 左侧大卡片：header + main -->
    <div class="left-card">
      <div class="header">
        <div class="breadcrumb">
          <span class="breadcrumb-link" @click="goToDatasetList">根目录 / </span>
          <span class="current">{{ kbInfo.kbName }}</span>
        </div>
        <div class="tabs">
          <span class="tab active">数据集</span>
          <span class="tab">搜索测试</span>
        </div>
      </div>
      <div class="main">
        <div class="file-list-bar">
          <div class="file-list-title">
            <i class="fas fa-list-ul"></i>
            <span>文件({{ files.length }})</span>
          </div>
        <div class="header-actions">
          <input class="search-input" placeholder="搜索" />
            <div class="import-dropdown-wrapper"
                 @mouseenter="showDropdown = true"
                 @mouseleave="showDropdown = false">
              <button class="import-btn">
                新建/导入
                <i class="fas fa-chevron-down" style="margin-left:6px;font-size:0.9em;"></i>
              </button>
              <div v-if="showDropdown" class="import-dropdown">
                <div
                  class="import-dropdown-item"
                  v-for="item in importOptions"
                  :key="item.type"
                  @click="handleImport(item.type)"
                >
                  <i :class="item.icon"></i>
                  <span>{{ item.label }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 左侧文件表格 -->
        <div class="file-list">
          <div class="file-table">
            <div class="file-table-header">
              <span style="flex: 1.6">名称</span>
              <span style="flex: 1">处理模式</span>
              <span style="flex: 1">数据量</span>
              <span style="flex: 1.4">创建/更新时间</span>
              <span style="flex: 1">状态</span>
              <span style="flex: 1">启用</span>
              <span style="flex: 0.8"></span>
            </div>
            <div class="file-table-body">
              <div class="file-table-row" v-for="file in files" :key="file.fileId">
                <span class="file-name-col">
                  {{ file.fileName }}
                </span>
                <span>
                  {{ file.fileProcessMethod === 'split' ? '分块存储' : '问答对提取' }}
                </span>
                <span>{{ file.fileSplitNum }}</span>
                <span class="file-date-col">
                  {{ file.createdAt }}
                  <br />
                  {{ file.updatedAt }}
                </span>
                <span class="file-status-col">
                  <div class="status-container">
                    <span class="status-dot" :class="getStatusClass(file.fileStatus)"></span>
                    {{ getStatusText(file.fileStatus) }}
                    <i v-if="file.fileStatus === 'ready'" class="status-icon iconfont icon-check"></i>
                  </div>
                </span>
                <span class="file-enabled-col">
                  <label class="switch" :class="{ 'disabled': !hasWritePermission }">
                    <input 
                      type="checkbox" 
                      :checked="file.isUse"
                      :disabled="!hasWritePermission"
                      @change="(e: Event) => handleFileStatusChange(file, (e.target as HTMLInputElement).checked)"
                    />
                    <span class="slider round"></span>
                  </label>
                </span>
                <span class="file-actions-col">
                  <i class="delete-icon fas fa-trash-alt" @click="handleDelete(file)"></i>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 右侧大卡片：detail-card -->
    <div class="detail-card">
      <div class="card-header">
        <div class="avatar" :style="{ background: kbInfo.kbBg }">
          <i :class="kbInfo.kbIcon"></i>
        </div>
        <div class="card-title-group">
          <div class="card-title">{{ kbInfo.kbName }}</div>
        </div>
      </div>
      <div class="card-type">{{ kbInfo.kbTypeName }}</div>
      <div class="card-desc">{{ kbInfo.kbDesc || '这个知识库还没有介绍~' }}</div>
      <div class="card-info">
        <div class="info-row vertical">
          <span class="info-label">知识库ID</span>
          <span class="info-value">{{ kbId }}</span>
        </div>
        <div class="info-row vertical">
          <span class="info-label">索引模型</span>
          <select v-model="embeddingModel" :disabled="!hasWritePermission" @change="handleModelChange('embedding')">
            <option v-for="item in modelOptions.embeddingModels" :key="item" :value="item">
              {{ item }}
            </option>
          </select>
        </div>
        <div class="info-row vertical">
          <span class="info-label">文本理解模型</span>
          <select v-model="chatModel" :disabled="!hasWritePermission" @change="handleModelChange('chat')">
            <option v-for="item in modelOptions.chatModels" :key="item" :value="item">
              {{ item }}
            </option>
          </select>
        </div>
        <div class="info-row vertical">
          <span class="info-label">图片理解模型</span>
          <select v-model="imageModel" :disabled="!hasWritePermission" @change="handleModelChange('image')">
            <option v-for="item in modelOptions.imageModels" :key="item" :value="item">
              {{ item }}
            </option>
          </select>
        </div>
      </div>
      <div class="card-collaborators">
        <div class="collab-title">协作者</div>
        <div class="collab-empty">暂无协作者</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useKnowledgeStore } from '@/store/knowledge';
import { validateKb, getModelOptions, getKBFiles, updateFileStatus, updateModel, type FileInfo, deleteFile } from '@/api/kb';
import { cookieUtils } from '@/utils/cookie';

const route = useRoute();
const router = useRouter();
const knowledgeStore = useKnowledgeStore();
const kbInfo = knowledgeStore.datasetInfo;
const embeddingModel = ref('');
const chatModel = ref('');
const imageModel = ref('');

// 从 kbInfo 获取权限
const hasWritePermission = computed(() => kbInfo.accessLevel === 'write');

const kbId = route.query.kbId as string || '';

const modelOptions = ref<{
  chatModels: string[];
  embeddingModels: string[];
  imageModels: string[];
}>({
  chatModels: [],
  embeddingModels: [],
  imageModels: []
});

// New method for navigation
const goToDatasetList = () => {
  router.push('/dataset/list');
};

// 替换示例文件数据
const files = ref<FileInfo[]>([]);

onMounted(async () => {
  let username = '';
  const userCookie = cookieUtils.getCookie('user');
  if (userCookie) {
    try {
      const userObj = JSON.parse(userCookie);
      username = userObj.username || '';
    } catch (e) {
      username = '';
    }
  }
  const res_kb = await validateKb(kbId);
  console.log(res_kb)
  if (!res_kb.exists) {
    router.push('/dataset/list');
  }

  try {
    const options = await getModelOptions();
    modelOptions.value = options;
    
    embeddingModel.value = kbInfo.embeddingModel;
    chatModel.value = kbInfo.chatModel;
    imageModel.value = kbInfo.imageModel;
  } catch (err: unknown) {
    console.error('Failed to fetch model options:', err);
  } 

  try {
    const response = await getKBFiles(kbId);
    console.log('response', response)
    files.value = response.files;
  } catch (error) {
    console.error('Failed to fetch KB files:', error);
  }
});

// 更新状态类获取函数
const getStatusClass = (status: string) => {
  switch (status) {
    case 'ready': return 'status-ready';
    case 'processing': return 'status-processing';
    case 'error': return 'status-error';
    default: return '';
  }
};

// 添加状态文字获取函数
const getStatusText = (status: string) => {
  switch (status) {
    case 'ready': return '已就绪';
    case 'processing': return '处理中';
    case 'error': return '失败';
    default: return status;
  }
};

// 更新删除处理函数
const handleDelete = async (file: FileInfo) => {
  // TODO: 添加确认对话框
  try {
    await deleteFile({
      fileId: file.fileId,
      kbId: kbId
    })
    
    const index = files.value.findIndex(f => f.fileId === file.fileId);
    if (index > -1) {
      files.value.splice(index, 1);
    }
  } catch (error) {
    console.error('Failed to delete file:', error)
  }
};

const showDropdown = ref(false);

const importOptions = [
  { icon: 'fas fa-align-left', label: '文本数据集', type: 'text' },
  { icon: 'fas fa-paste', label: '备份导入', type: 'backup' }
];

function handleImport(type: string) {
  showDropdown.value = false;
  // 这里写你的业务逻辑
  // if (type === 'text') ...
  router.push({ name: 'ImportUploadPage', query: { kbId: kbId, type: type } });
}

// 更新文件状态切换处理函数
const handleFileStatusChange = async (file: FileInfo, newStatus: boolean) => {
  if (!hasWritePermission.value) {
    return; // 如果没有写权限，直接返回
  }

  try {
    await updateFileStatus({
      fileId: file.fileId,
      isUse: newStatus
    });
    // 更新本地状态
    const index = files.value.findIndex(f => f.fileId === file.fileId);
    if (index > -1) {
      files.value[index].isUse = newStatus;
    }
  } catch (error) {
    console.error('Failed to update file status:', error);
    // 如果更新失败，恢复开关状态
    const index = files.value.findIndex(f => f.fileId === file.fileId);
    if (index > -1) {
      files.value[index].isUse = !newStatus;
    }
  }
};

// 添加模型更新处理函数
const handleModelChange = async (type: 'embedding' | 'chat' | 'image') => {
  if (!hasWritePermission.value) {
    return;
  }

  try {
    await updateModel({
      kbId: kbId,
      modelType: type,
      modelName: type === 'embedding' ? embeddingModel.value : 
                type === 'chat' ? chatModel.value : 
                imageModel.value
    });
    
    // 更新成功后更新store中的数据
    if (kbInfo) {
      kbInfo.embeddingModel = embeddingModel.value;
      kbInfo.chatModel = chatModel.value;
      kbInfo.imageModel = imageModel.value;
    }
  } catch (error) {
    console.error('Failed to update model:', error);
    // 如果更新失败，恢复原来的值
    if (kbInfo) {
      embeddingModel.value = kbInfo.embeddingModel;
      chatModel.value = kbInfo.chatModel;
      imageModel.value = kbInfo.imageModel;
    }
  }
};
</script>

<style scoped>
.knowledge-detail-layout {
  display: flex;
  gap: 8px;
  padding: 10px;
  background: #18191c;
  min-height: 100%;
  width: 100%;
}

.left-card {
  background: #232428;
  border-radius: 16px;
  box-shadow: 0 2px 12px #0001;
  flex: 2;
  display: flex;
  flex-direction: column;
  padding: 0 0 24px 0;
  min-width: 0;
}

.header {
  padding: 12px 32px 12px 32px;
  display: flex;
  align-items: center;
  gap: 24px;
  margin-bottom: 16px;
  border-bottom: 1px solid #3a3a3a;
}

.breadcrumb {
  color: #bfc1c8;
  font-size: 1rem;
}

.breadcrumb-link {
  cursor: pointer;
  color: #bfc1c8;
  padding-right: 2px;
}
.breadcrumb-link:hover {
  text-decoration: none;
  background-color: #383838;
  border-radius: 6px;
}

.breadcrumb .current {
  color: #fff;
  font-weight: bold;
}

.tabs {
  display: flex;
  gap: 16px;
  margin-left: auto;
  margin-right: auto;
}

.tab {
  color: #bfc1c8;
  font-size: 1rem;
  cursor: pointer;
  padding: 4px 12px;
  border-radius: 66px;
}

.tab.active {
  color: #4c6fff;
}

.header-actions {
  display: flex;
  gap: 12px;
  margin-left: auto;
}

.search-input {
  padding: 6px 14px;
  border: 1px solid #3a3a3a;
  border-radius: 8px;
  font-size: 1rem;
  background: #18191c;
  color: #f5f6fa;
  height: 42px;
}

.tag-select {
  padding: 6px 14px;
  border: 1px solid #e0e3e8;
  border-radius: 8px;
  font-size: 1rem;
  background: #fff;
}

.import-dropdown-wrapper {
  position: relative;
  display: inline-block;
  margin-bottom: 15px;
}

.import-btn {
  position: relative;
  z-index: 2;
  height: 40px;
  background: linear-gradient(90deg, #7a7fff 0%, #7fdcff 100%);
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 6px 18px;
  margin-bottom: 5px;
  font-size: 1rem;
  cursor: pointer;
  font-weight: bold;
}

.import-dropdown {
  position: absolute;
  right: 0;
  top: 100%;
  min-width: 140px;
  width: 140px;
  background: #393a3a;
  border-radius: 8px;
  box-shadow: 0 2px 12px #0001;
  z-index: 1000;
}

.import-dropdown-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 0px;
  padding-left: 15px;
  cursor: pointer;
  transition: all 0.3s;
}

.import-dropdown-item:hover {
  background-color: #2c2626;
}

.main {
  padding: 0 32px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.file-list {
  flex: 2;
  background: #232428;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  min-width: 0;
  margin-top: 10px;
}

.file-table {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px #0003;
  border: 1px solid #525255;
}

.file-table-header {
  display: flex;
  justify-content: space-between;
  background-color: #2a2b2f;
  color: #bfc1c8;
  font-size: 0.98rem;
  font-weight: 500;
  padding: 12px 0;
  border-bottom: 1px solid #4f5057;
  margin-bottom: 0;
}

/* Explicitly set text alignment for each header span */
.file-table-header span:nth-child(1) { text-align: left; } /* 名称 */
.file-table-header span:nth-child(2) { text-align: center; } /* 处理模式 */
.file-table-header span:nth-child(3) { text-align: center; } /* 数据量 */
.file-table-header span:nth-child(4) { text-align: center; } /* 创建/更新时间 */
.file-table-header span:nth-child(5) { text-align: center; } /* 状态 */
.file-table-header span:nth-child(6) { text-align: center; } /* 启用 */
.file-table-header span:nth-child(7) { text-align: center; } /* Actions */


.file-table-body {
  flex: 1;
}

.file-table-row {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #2e2f34;
  color: #f5f6fa;
  background: #232428 !important;
}

.file-table-row:last-child {
  border-bottom: none; /* Remove bottom border for the last row */
}

.file-table-row:nth-child(even) {
  background-color: #25262b !important;
}

/* Ensure row spans have the correct flex properties and text alignment */
.file-table-row span {
   padding: 3px 0;
   font-size: 0.9rem;
   color: #f5f6fa;
   /* No generic flex: 1 here */
}

/* Define specific flex properties for corresponding columns in header and rows */
.file-table-header span:nth-child(1),
.file-table-row span.file-name-col { /* 名称 */
  flex: 1.6 1; /* flex-grow, flex-shrink, flex-basis */
  margin-left: 15px;
}

.file-table-header span:nth-child(2),
.file-table-row span:nth-child(2) { /* 处理模式 */
  flex: 1 1;
  text-align: center;
}

.file-table-header span:nth-child(3),
.file-table-row span:nth-child(3) { /* 数据量 */
  flex: 1 1 ; /* Don't grow, allow shrinking, small basis */
  text-align: center;
}

.file-table-header span:nth-child(4),
.file-table-row span.file-date-col { /* 创建/更新时间 */
  flex: 1.4 1;
  text-align: center;
}

.file-table-header span:nth-child(5),
.file-table-row span.file-status-col { /* 状态 */
  flex: 1 1 ;
  /* Remove text-align here, controlled by flexbox inside */
}

.file-table-header span:nth-child(6),
.file-table-row span.file-enabled-col { /* 启用 */
  flex: 1 1 ;
text-align: center; /* Center the toggle switch */
}

.file-table-header span:nth-child(7),
.file-table-row span.file-actions-col { /* Actions */
  flex: 0.8 1 ;
  text-align: center; /* Align actions button to the right */
}


.file-name-col {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-icon {
  font-size: 1.2rem;
  color: #999;
}

/* Add specific icon styles here, e.g. */
.icon-txt-file::before { content: '\e60c'; /* Replace with your iconfont code */ }
.icon-csv-file::before { content: '\e60d'; /* Replace with your iconfont code */ }
.icon-file::before { content: '\e60e'; /* Replace with your iconfont code */ }


.file-date-col {
  line-height: 1.4;
  font-size: 0.85rem; /* Slightly smaller font for dates */
  color: #888;
}

/* Updated style for the status column with reduced padding */
.file-status-col {
  display: flex;
  align-items: center;
  justify-content: center; /* Center content horizontally */
  gap: 4px; /* Keep gap */
}

.status-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-size: 0.7rem !important;
  background-color: rgba(76,175,80,0.12);
  border-radius: 10px;
  padding: 4px 8px;
  color: #4caf50;
  width: fit-content;
  margin: 0 auto;
}

/* Style for the status dot */
.status-dot {
  display: inline-block;
  width: 6px; /* Reduced dot size */
  height: 6px; /* Reduced dot size */
  border-radius: 50% !important; /* This makes it a circle */
  background-color: #4caf50; /* Green color for ready status */
  flex-shrink: 0; /* Prevent shrinking */
}

/* Status icon color is handled by .file-status-col */
.status-icon {
  font-size: 0.6rem; /* Keep smaller icon size */
  /* color is inherited from .file-status-col */
}
/* Add toggle switch styles */
.switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #444;
  transition: .4s;
  border-radius: 20px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #4c6fff;
}

input:focus + .slider {
  box-shadow: 0 0 1px #5b8cff;
}

input:checked + .slider:before {
  transform: translateX(20px);
}

.file-actions-col {
  text-align: center;
}

.delete-icon {
  cursor: pointer;
  color: #888;
  font-size: 16px;
  transition: all 0.3s;
}

.delete-icon:hover {
  color: #ff4d4f;
  transform: scale(1.1);
}

/* Right card styles remain the same */
.detail-card {
  background: #232428;
  border-radius: 16px;
  box-shadow: 0 2px 12px #0001;
  flex: 1;
  min-width: 280px;
  max-width: 280px;
  padding: 15px 24px;
  display: flex;
  flex-direction: column;
  gap: 18px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 16px;
}

.avatar {
  width: 35px;
  height: 35px;
  border-radius: 12px;
  background: #e6eaf3;
  display: flex;
  align-items: center;
  justify-content: center;
}
.avatar i {
  font-size: 20px;
  color: rgb(233, 235, 240);
}

.card-title-group {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.card-title {
  font-size: 1.2rem;
  font-weight: bold;
  color: #fff;
}

.card-type {
  width: fit-content;
  color: #4c6fff;
  font-size: 0.8rem;
  border: 1px solid #5b8cff;
  border-radius: 6px;
  padding: 2px 4px;
  background-color: #96b3ed;
}

.card-desc {
  color: #999;
  font-size: 0.8rem;
  margin-bottom: 10px;
}

.card-info {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.info-row {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 0.98rem;
  margin-bottom: 10px;
}

.info-label {
  color: #999;
  min-width: 90px;
  font-size: 0.75rem;
  font-weight: 500;
  line-height: 1.2;
}

.info-value {
  color: #fff;
  font-weight: 500;
  font-size: 0.7rem;
}

.card-collaborators {
  margin-top: 5px;
}

.collab-title {
  color: #fff;
  font-size: 0.8rem;
  font-weight: 500;
  margin-bottom: 6px;
}

.collab-empty {
  color: #888;
  background: #18191c;
  border-radius: 8px;
  padding: 12px 0;
  text-align: center;
}

.info-row.vertical {
  flex-direction: column;
  align-items: flex-start;
  gap: 2px;
}

select, select option {
  color: #f5f6fa;
  background: #18191c;
}

select {
  width: 220px;
  padding: 8px 12px;
  border-radius: 8px;
  border: 1px solid #3a3a3a;
  font-size: 0.8rem;
  background: #18191c;
  color: #f5f6fa;
}

.file-list-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.file-list-title {
  display: flex;
  align-items: center;
  font-size: 1rem;
  color: #dee0e9;
  font-weight: bold;
  gap: 6px;
  margin-bottom: 0;
}

.file-list-title i {
  font-size: 0.9rem;
  color: #e4e5ec;
  margin-right: 2px;
}

.file-list-title span {
  color: #e3e5ec;
  font-size: 0.9rem;
  font-weight: 500;
  letter-spacing: 1px;
}

/* 添加新的状态样式 */
.status-processing {
  background-color: rgba(255, 152, 0, 0.12);
  color: #ff9800;
}

.status-error {
  background-color: rgba(244, 67, 54, 0.12);
  color: #f44336;
}

.status-dot.status-processing {
  background-color: #ff9800;
}

.status-dot.status-error {
  background-color: #f44336;
}

/* 添加禁用状态的样式 */
.switch.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.switch.disabled input {
  cursor: not-allowed;
}

.switch.disabled .slider {
  cursor: not-allowed;
}

select:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #2a2b2f;
}
</style>
