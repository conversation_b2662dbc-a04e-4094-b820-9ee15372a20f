<template>
  <div class="workflow-page-container">
    <!-- Left Navigation/Filter Panel -->
    <aside class="workflow-sidebar">
      <div class="sidebar-menu">
        <template v-for="item in menuItems" :key="item.appMenuId">
          <div v-if="item.children" class="sidebar-group">
            <!-- Group Header -->
            <div class="group-header" @click="toggleGroup(item)">
              <i v-if="item.appMenuIcon" :class="item.appMenuIcon"></i>
              <span>{{ item.appMenuName }}</span>
              <i class="fas" :class="item.appMenuExpanded ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
            </div>
            <!-- Group Items -->
            <div class="group-items" v-if="item.appMenuExpanded">
              <div 
                v-for="child in item.children" 
                :key="child.appMenuId"
                class="menu-item"
                :class="{ active: activeMenuItem === child.appMenuId }" 
                @click="handleMenuItemClick(child)"
              >
                 <i v-if="child.appMenuIcon" :class="child.appMenuIcon"></i>
                 <span>{{ child.appMenuName }}</span>
              </div>
            </div>
          </div>
          <div v-else class="sidebar-group">
             <!-- Single Item (not part of a collapsible group) -->
             <div 
                class="menu-item"
                :class="{ active: activeMenuItem === item.appMenuId }"
                @click="handleMenuItemClick(item)"
             >
                <i v-if="item.appMenuIcon" :class="item.appMenuIcon"></i>
                <span>{{ item.appMenuName }}</span>
             </div>
          </div>
        </template>
      </div>
    </aside>

    <!-- Right Main Content Area -->
    <main class="workflow-main-content">
      <!-- Header -->
      <div class="page-header">
        <h2>{{ currentPageTitle }}</h2>
        <div class="header-actions">
          <div class="search-input-wrapper">
             <i class="fas fa-search search-icon"></i>
             <input 
               type="text" 
               placeholder="搜索应用" 
               class="search-input"
               v-model="searchKeyword"
             >
          </div>
          <button
            class="new-button"
            ref="newBtnRef"
            @mouseenter="showDropdown"
            @mouseleave="hideDropdown"
          >+ 新建</button>
          <teleport to="body">
            <div
              v-if="dropdownVisible"
              :style="{position: 'absolute', top: dropdownPos.top + 'px', left: dropdownPos.left + 'px', zIndex: 99999}"
              @mouseenter="cancelHide"
              @mouseleave="hideDropdown"
            >
              <NewDropdownMenu @select="handleDropdownSelect" />
            </div>
          </teleport>
        </div>
      </div>

      <!-- Applications Grid -->
      <div class="applications-grid">
        <div 
          v-for="app in displayedApps" 
          :key="app.appId" 
          class="app-card"
          @click="handleCardClick(app)"
        >
          <div class="card-header">
             <div class="card-icon" :style="{background: app.appBg}">
                <i :class="app.appIcon"></i>
             </div>
             <div class="card-title">{{ app.appName }}</div>
             <div class="card-type-tag">{{ app.appTypeName }}</div>
          </div>
          
          <div class="card-description">{{ app.appDesc }}</div>
          
          <div class="card-footer">
            <div class="footer-left">
              <div class="card-author">
                <span>{{ app.creatorName }}</span>
              </div>
              <span class="card-privacy">{{ app.appPrivacy === 'private' ? '私有' : '公开' }}</span>
              <span class="card-status">{{ app.appStatus === 'published' ? '已发布' : '未发布' }}</span>
            </div>
            <div class="card-date-wrapper">
              <span
                v-if="showDotsForApp !== app.appId"
                class="card-date"
                :ref="`dateRef-${app.appId}`"
                @mouseenter="showDots(app.appId)"
                @mouseleave="hideDots(app.appId)"
              >{{ app.updatedAt }}</span>
              <span
                v-if="showDotsForApp === app.appId"
                class="card-dots"
                @mouseenter="showActionDropdown(app.appId, $event)"
                @mouseleave="hideDotsAndDropdown(app.appId)"
              >•••</span>
              <teleport to="body">
                <div
                  v-if="actionDropdownVisible && activeAppId === app.appId"
                  :style="{
                    position: 'absolute',
                    top: actionDropdownPos.top + 'px',
                    left: actionDropdownPos.left + 'px',
                    zIndex: 99999
                  }"
                  @mouseenter="cancelActionHide"
                  @mouseleave="hideActionDropdown"
                >
                  <AppActionDropdown
                    :appId="app.appId"
                    @action="handleAppAction"
                  />
                </div>
              </teleport>
            </div>
          </div>
        </div>
      </div>

      <CreateAppModal
        v-if="showCreateModal"
        :appTypeId="appTypeId"
        :appTypeName="appTypeName"
        :appTypeIcon="appTypeIcon"
        :appTypeBg="appTypeBg"
        @close="showCreateModal = false"
      />
    </main>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted } from 'vue';
import '@fortawesome/fontawesome-free/css/all.css';
import NewDropdownMenu from './NewDropdownMenu.vue';
import CreateAppModal from './CreateAppModal.vue';
import AppActionDropdown from './AppActionDropdown.vue';
import { appMenuApi } from '@/api/appMenu';
import { appsApi, AppItem } from '@/api/apps';
import { cookieUtils } from '@/utils/cookie';
import router from '@/router';

interface AppMenuItem {
  appMenuId: string;
  appMenuName: string;
  appMenuIcon: string;
  appMenuExpanded: boolean;
  children?: AppMenuItem[];
}

const apps = ref<AppItem[]>([]);

onMounted(async () => {
  try {
    // 从cookie获取username
    const userCookie = cookieUtils.getCookie('user');
    let username = '';
    if (userCookie) {
      try {
        const userObj = JSON.parse(userCookie);
        username = userObj.username;
      } catch (e) {
        console.error('解析user cookie失败', e);
      }
    }

    const res = await appMenuApi.getAppMenu();
    console.log(res);
    // 设置第一个分组展开，其他收起
    menuItems.value = (Array.isArray(res.menu) ? res.menu : []).map((item, index) => ({
      ...item,
      appMenuExpanded: index === 0
    }));

    
    if (username) {
      const appsRes = await appsApi.getUserApps(username);
      apps.value = Array.isArray(appsRes.apps) ? appsRes.apps : [];
    } else {
      apps.value = [];
      console.error('未获取到用户名，无法加载应用列表');
    }
  } catch (e) {
    console.error('获取菜单或应用失败', e);
    menuItems.value = [];
    apps.value = [];
  }
});

// 菜单数据结构
const menuItems = ref<AppMenuItem[]>([]);

// 当前激活的菜单项
const activeMenuItem = ref('all');

// 根据当前选中的菜单项计算页面标题
const currentPageTitle = computed(() => {
  // 遍历顶层菜单项
  for (const item of menuItems.value) {
    if (item.appMenuId === activeMenuItem.value) {
      return item.appMenuName; // Found a top-level item
    }
    // 如果是分组，遍历其子项
    if (item.children) {
      for (const child of item.children) {
        if (child.appMenuId === activeMenuItem.value) {
          return item.appMenuName; // Return group label if a child is selected
        }
      }
    }
  }
  // Fallback if activeMenuItem doesn't match any known item (shouldn't happen if handled correctly)
  // Alternatively, you could return a default title or the label of the 'all' item
  const allItem = menuItems.value.find(item => item.children?.some(child => child.appMenuId === 'all'));
  return allItem ? allItem.appMenuName : '团队应用'; // Default to '团队应用' or parent group label
});

// 根据当前选中的菜单项过滤应用列表
const filteredApps = computed(() => {
  if (activeMenuItem.value === 'all') {
    return apps.value;
  }
  // Assuming 'simple', 'workflow', 'plugin' are the values corresponding to app types
  if (activeMenuItem.value === 'simple' || activeMenuItem.value === 'workflow' || activeMenuItem.value === 'plugin') {
     return apps.value.filter(app => app.appTypeId === activeMenuItem.value);
  }
  
  // Handle other menu items if needed, e.g., System Plugins or Template Market items
  // For now, return empty array for unhandled selections
  console.warn(`Unhandled menu item selection: ${activeMenuItem.value}`);
  return [];
});

// 搜索关键词
const searchKeyword = ref('');

// 根据搜索关键词过滤应用列表
const displayedApps = computed(() => {
  if (!searchKeyword.value) {
    return filteredApps.value;
  }
  const keyword = searchKeyword.value.toLowerCase();
  return filteredApps.value.filter(app => 
    app.appName.toLowerCase().includes(keyword) || 
    app.appDesc.toLowerCase().includes(keyword)
  );
});

const handleCardClick = (app: AppItem) => {
  router.push({
    path: '/app/detail',
    query: { appId: app.appId }
  });
}

const handleMenuItemClick = (item: AppMenuItem) => {
  activeMenuItem.value = item.appMenuId;
}

const newBtnRef = ref<HTMLElement | null>(null);
const dropdownVisible = ref(false);
const dropdownPos = ref({ top: 0, left: 0 });
let hideTimer: any = null;

function showDropdown() {
  if (hideTimer) clearTimeout(hideTimer);
  dropdownVisible.value = true;
  nextTick(() => {
    if (newBtnRef.value) {
      const rect = newBtnRef.value.getBoundingClientRect();
      dropdownPos.value = {
        top: rect.bottom + window.scrollY + 6,
        left: rect.right + window.scrollX - 280  // 再向左移动10px
      };
    }
  });
}
function hideDropdown() {
  hideTimer = setTimeout(() => {
    dropdownVisible.value = false;
  }, 120);
}
function cancelHide() {
  if (hideTimer) clearTimeout(hideTimer);
}
function handleDropdownSelect(val: { appTypeId: string; appTypeName: string; appTypeIcon: string; appTypeBg: string }) {
  dropdownVisible.value = false;
  appTypeId.value = val.appTypeId;
  appTypeName.value = val.appTypeName;
  appTypeIcon.value = val.appTypeIcon;
  appTypeBg.value = val.appTypeBg;
  showCreateModal.value = true;
}

// 展开/收起分组
const toggleGroup = (group: AppMenuItem) => {
  if (group.appMenuExpanded !== undefined) {
    group.appMenuExpanded = !group.appMenuExpanded;
  }
};


const showCreateModal = ref(false);
const appTypeId = ref('');
const appTypeName = ref('');
const appTypeIcon = ref('');
const appTypeBg = ref('');

// 应用操作下拉菜单相关
const actionDropdownVisible = ref(false);
const actionDropdownPos = ref({ top: 0, left: 0 });
const activeAppId = ref('');
const showDotsForApp = ref('');
let actionHideTimer: any = null;
let dotsHideTimer: any = null;

function showDots(appId: string) {
  if (dotsHideTimer) clearTimeout(dotsHideTimer);
  showDotsForApp.value = appId;
}

function hideDots(appId: string) {
  dotsHideTimer = setTimeout(() => {
    if (showDotsForApp.value === appId) {
      showDotsForApp.value = '';
    }
  }, 100);
}

function showActionDropdown(appId: string, event: MouseEvent) {
  if (actionHideTimer) clearTimeout(actionHideTimer);
  if (dotsHideTimer) clearTimeout(dotsHideTimer);
  activeAppId.value = appId;
  actionDropdownVisible.value = true;

  nextTick(() => {
    const target = event.target as HTMLElement;
    if (target) {
      const rect = target.getBoundingClientRect();
      actionDropdownPos.value = {
        top: rect.bottom + window.scrollY + 6,
        left: rect.left + window.scrollX - 80  // 向左偏移一些
      };
    }
  });
}

function hideActionDropdown() {
  actionHideTimer = setTimeout(() => {
    actionDropdownVisible.value = false;
    activeAppId.value = '';
    showDotsForApp.value = '';
  }, 120);
}

function hideDotsAndDropdown(appId: string) {
  actionHideTimer = setTimeout(() => {
    actionDropdownVisible.value = false;
    activeAppId.value = '';
    showDotsForApp.value = '';
  }, 120);
}

function cancelActionHide() {
  if (actionHideTimer) clearTimeout(actionHideTimer);
}

function handleAppAction(payload: { action: string; appId: string }) {
  actionDropdownVisible.value = false;
  activeAppId.value = '';

  console.log('应用操作:', payload);

  // 根据不同的操作执行相应的逻辑
  switch (payload.action) {
    case 'edit':
      console.log('编辑应用信息:', payload.appId);
      // TODO: 打开编辑对话框
      break;
    case 'move':
      console.log('移动应用:', payload.appId);
      // TODO: 打开移动对话框
      break;
    case 'permission':
      console.log('设置权限:', payload.appId);
      // TODO: 打开权限设置对话框
      break;
    case 'export':
      console.log('导出应用:', payload.appId);
      // TODO: 执行导出逻辑
      break;
    case 'delete':
      console.log('删除应用:', payload.appId);
      // TODO: 显示确认删除对话框
      break;
    default:
      console.log('未知操作:', payload.action);
  }
}
</script>

<style scoped>
.workflow-page-container {
  display: flex;
  min-height: calc(100vh - 4rem);
  background-color: var(--color-background-soft); /* 使用主题色 */
}

.workflow-sidebar {
  width: 180px; /* 调整宽度以容纳更多内容 */
  background: linear-gradient(135deg, #1a1a1a, #2a2a2a);
  border-right: 1px solid var(--color-border);
  padding: 1rem 0.5rem;
  flex-shrink: 0;
  color: var(--color-text-soft);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  transition: transform 0.3s ease;
}

.workflow-sidebar:hover {
  transform: translateX(5px);
}

.sidebar-group {
  margin-bottom: 1rem;
}

.group-header {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  font-weight: bold;
  color: #fff;
  cursor: pointer;
  margin: 0 0.5rem;
  font-size: 0.95rem;
  text-shadow: 0 2px 8px #1e90ff55, 0 0 2px #fff;
  background: linear-gradient(90deg, #1a1a1a 60%, #2a2a2a 100%);
  border-radius: 0.4rem;
  letter-spacing: 0.02em;
}

.group-header i {
  margin-right: 0.5rem;
  color: var(--color-text-soft);
}

.group-header .fas:last-child {
  margin-left: auto;
  margin-right: 0;
  font-size: 0.8em;
  transition: transform 0.2s;
}

.group-header .fas:last-child.fa-chevron-down {
   transform: rotate(180deg);
}

.group-items {
  padding-left: 0.8rem; /* 增加缩进 */
}

.menu-item {
  padding: 0.6rem 1rem;
  cursor: pointer;
  color: var(--color-text-soft);
  transition: background 0.2s, color 0.2s, box-shadow 0.2s;
  border-radius: 0.35rem;
  margin: 0.3rem 0.5rem;
  display: flex;
  align-items: center;
  font-size: 0.92rem;
  position: relative;
  z-index: 1;
}

.menu-item.active {
  background: rgba(30, 144, 255, 0.12);
  color: #4fc3ff;
  font-weight: bold;
  backdrop-filter: blur(2.5px);
  border: 1.5px solid #4fc3ff55;
}

.menu-item:hover:not(.active) {
  background: rgba(30, 144, 255, 0.08);
  color: #b3e0ff;
  text-shadow: 0 0 6px #4fc3ff55;
}

.menu-item i {
  margin-right: 0.5rem;
  font-size: 1.1em;
  filter: drop-shadow(0 0 2px #4fc3ff55);
}

.workflow-main-content {
  flex-grow: 1;
  padding: 1.5rem;
  color: var(--color-text);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.page-header h2 {
  font-size: 1.8rem;
  font-weight: bold;
  color: var(--color-text);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.search-input-wrapper {
   position: relative;
   display: flex;
   align-items: center;
}

.search-input {
  padding: 0.5rem 1rem 0.5rem 2.5rem;
  border: 1px solid var(--color-border);
  border-radius: 0.25rem;
  background: linear-gradient(90deg, #413d3f 0%, #323133 100%);
  color: #f8f6f6;
  transition: border-color 0.3s, background 0.3s;
  width: 200px;
}

.search-input:focus {
  outline: none;
  border-color: #bbaecb;
  background: linear-gradient(90deg, #535155 0%, #5f575c 100%);
}

.search-icon {
  position: absolute;
  left: 1rem;
  color: #bbaecb;
}

.new-button {
  padding: 0.5rem 1.5rem;
  background: linear-gradient(90deg, #6a6ff7 0%, #6fd0ff 100%);
  color: #fff;
  border: none;
  border-radius: 0.8rem;
  font-size: 1.1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s, box-shadow 0.2s;
  box-shadow: 0 2px 12px #6a6ff733;
  letter-spacing: 0.02em;
}

.new-button:hover {
  background: linear-gradient(90deg, #7a7fff 0%, #7fdcff 100%);
  box-shadow: 0 4px 18px #6fd0ff55;
}

.applications-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 2rem;
}

.app-card {
  background: linear-gradient(135deg, #23242a 80%, #18181c 100%);
  border-radius: 1.2rem;
  box-shadow: 0 4px 32px 0 #000a, 0 1.5px 8px 0 #4fc3ff11 inset;
  border: none;
  padding: 1.5rem 1.2rem 1.2rem 1.2rem;
  display: flex;
  flex-direction: column;
  gap: 1.1rem;
  color: #cfd8dc;
  position: relative;
  transition: transform 0.18s, box-shadow 0.18s;
}

.app-card:hover {
  transform: translateY(-4px) scale(1.025);
  box-shadow: 0 8px 36px 0 #4fc3ff33, 0 2px 12px 0 #000c;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 0.7rem;
  margin-bottom: 0.2rem;
}

.card-icon {
  width: 35px;
  height: 35px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #f0f3f5;
  box-shadow: 0 2px 12px #4fc3ff22;
  font-size: 1.2rem;
  flex-shrink: 0;
  
}

.card-title {
  font-size: 0.9rem;
  color: #fff;
  margin-bottom: 0.1rem;
}

.card-type-tag {
  margin-left: auto;
  font-size: 0.7rem;
  color: #4fc3ff;
  background: rgba(79, 195, 255, 0.10);
  font-weight: 400;
  padding: 0.18rem 0.7rem;
  border-radius: 0.6rem;
  letter-spacing: 0.01em;
  align-self: center;
  box-shadow: 0 1px 6px #4fc3ff11;
}

.card-description {
  font-size: 0.75rem;
  color: #b0b8c1;
  margin-bottom: 0.8rem;
  flex-grow: 1;
  text-align: left;
  padding-left: 10px;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.98rem;
  color: #b0b8c1;
  margin-top: auto;
  border-top: none;
  padding-top: 0.2rem;
}

.footer-left {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.card-author {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.7rem;
  padding-left: 10px;
}

.card-privacy {
  color: #4fc3ff;
  font-size: 0.7rem;
  margin-left: 15px;
}

.card-status {
  color: #4fc3ff;
  font-size: 0.7rem;
  margin-left: 15px;
}

.card-date-wrapper {
  position: relative;
}

.card-date {
  color: #b0b8c1;
  font-size: 0.7rem;
  margin-right: 10px;
  cursor: pointer;
  padding: 0.2rem 0.4rem;
  border-radius: 0.3rem;
  transition: background 0.2s, color 0.2s;
}

.card-date:hover {
  background: rgba(79, 195, 255, 0.1);
  color: #4fc3ff;
}

.card-dots {
  color: #4fc3ff;
  font-size: 0.9rem;
  margin-right: 10px;
  cursor: pointer;
  padding: 0.2rem 0.4rem;
  border-radius: 0.3rem;
  transition: background 0.2s, color 0.2s;
  background: rgba(79, 195, 255, 0.1);
  font-weight: bold;
  letter-spacing: 0.1em;
}

.card-dots:hover {
  background: rgba(79, 195, 255, 0.2);
  color: #6fd0ff;
}
</style>