<template>
  <div class="home-layout">
    <nav class="navbar">
      <div class="navbar-content">
        <div class="navbar-wrapper">
          <!-- 左侧 Logo -->
          <div class="logo-container">
            <div class="logo-wrapper">
              <div class="logo">
                <img :src="jianyangImg" class="logo-icon" alt="logo" />
                <span class="logo-text">AI-Assistant</span>
              </div>
            </div>
          </div>

          <!-- 右侧用户信息 -->
          <div class="user-container">
            <div class="user-wrapper">
              <!-- 用户头像 -->
              <div class="user-dropdown">
                <button class="user-button">
                  <div class="avatar">
                    {{ userInitial }}
                  </div>
                  <span class="username">{{ userName }}</span>
                  <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
                </button>

                <!-- 下拉菜单 -->
                <div class="dropdown-menu">
                  <a href="#" class="dropdown-item">个人设置</a>
                  <a href="#" class="dropdown-item">修改密码</a>
                  <hr class="dropdown-divider">
                  <a href="#" @click="handleLogout" class="dropdown-item logout">退出登录</a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>
    <div class="main-area">
      <Sidebar />
      <main class="main-content">
        <!-- 这里可以添加主要内容 -->
        <router-view></router-view>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ArrowDown } from '@element-plus/icons-vue'
import jianyangImg from '@/assets/jianyang.png'
import { cookieUtils } from '@/utils/cookie'
import Sidebar from '@/components/Sidebar.vue'

const router = useRouter()

const getUserNameFromCookie = () => {
  const userCookie = cookieUtils.getCookie('user')
  if (userCookie) {
    try {
      const user = JSON.parse(userCookie)
      return user.username || '用户名'
    } catch {
      return '用户名'
    }
  }
  return '用户名'
}

const userName = ref(getUserNameFromCookie())
const userInitial = computed(() => userName.value.charAt(0).toUpperCase())

const checkUserCookie = () => {
  const userCookie = cookieUtils.getCookie('user')
  if (!userCookie) {
    router.push('/login')
  }else{
    router.push('/workflow/apps')
  }
}

onMounted(() => {
  checkUserCookie()
})

const handleLogout = async () => {
  try {
    cookieUtils.deleteCookie('user')
    router.push('/login')
  } catch (error) {
    console.error('Logout failed:', error)
  }
}
</script>

<style scoped>
.home-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.navbar {
  background-color: rgba(var(--dark-secondary-rgb), 0.8);
  backdrop-filter: blur(8px);
  border-bottom: 1px solid var(--dark-border);
  z-index: 9999999;
}

.navbar-content {
  max-width: 100%;
  margin: 0 auto;
  padding: 0 1rem;
}

.navbar-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 4rem;
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.logo-icon {
  height: 2.5rem;
  width: 2.5rem;
  object-fit: contain;
}

.logo-text {
  font-size: 1.25rem;
  font-weight: bold;
  background: linear-gradient(to right, #60a5fa, #a855f7);
  -webkit-background-clip: text;
  color: transparent;
}

.user-container {
  display: flex;
  align-items: center;
}

.user-wrapper {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-dropdown {
  position: relative;
  height: 3rem;
}

.user-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  height: 100%;
}

.user-button:focus {
  outline: none;
}

.avatar {
  width: 2rem;
  height: 2rem;
  border-radius: 9999px;
  background: linear-gradient(to right, #3b82f6, #a855f7);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 500;
  border: 2px solid var(--dark-border);
  transition: border-color 0.2s;
}

.avatar:hover {
  border-color: #60a5fa;
}

.username {
  color: var(--dark-text);
}

.dropdown-icon {
  width: 1rem;
  height: 1rem;
  color: var(--dark-text-secondary);
}

.dropdown-menu {
  position: absolute;
  right: 0;
  z-index: 99999;
  width: 7.5rem;
  border-radius: 0.375rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  padding: 0.25rem 0;
  background-color: var(--dark-secondary);
  border: 1px solid var(--dark-border);
  display: none;
}

.user-dropdown:hover .dropdown-menu {
  display: block;
  animation: fadeIn 0.2s ease-out;
}

.dropdown-item {
  display: block;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  color: var(--dark-text);
  text-decoration: none;
  transition: background-color 0.2s;
}

.dropdown-item:hover {
  background-color: var(--dark-accent);
}

.dropdown-divider {
  margin: 0.25rem 0;
  border: 0;
  border-top: 1px solid var(--dark-border);
}

.logout {
  color: #f87171;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (min-width: 640px) {
  .navbar-content {
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .navbar-content {
    padding: 0 2rem;
  }
  
}
.main-area {
  display: flex;
  height: calc(100vh - 4.25rem);
  z-index: inherit;
  min-height: 0;
  overflow: hidden;
}
.main-content {
  flex: 1;
  background: var(--dark-primary);
  margin: 0;
}
</style>
