<template>
  <aside class="sidebar">
    <div class="sidebar-menu">
      <router-link to="/chat" custom v-slot="{ href, navigate, isActive }">
        <a :href="href" @click="(e) => { console.log('Navigating to chat', href); navigate(e); }" class="sidebar-item" :class="{ active: isActive }">
          <i class="far fa-comment-dots icon"></i>
          <span>聊天</span>
        </a>
      </router-link>

      <router-link to="/workflow/apps" custom v-slot="{ href, navigate, isActive }">
        <a :href="href" @click="(e) => { console.log('Navigating to workbench apps', href); navigate(e); }" class="sidebar-item" :class="{ active: isActive }">
          <i class="far fa-folder-open icon"></i>
          <span>工作台</span>
        </a>
      </router-link>

      <router-link to="/dataset/list" custom v-slot="{ href, navigate, isActive }">
        <a :href="href" @click="(e) => { console.log('Navigating to knowledge list', href); navigate(e); }" class="sidebar-item" :class="{ active: isActive }">
          <i class="far fa-lightbulb icon"></i>
          <span>知识库</span>
        </a>
      </router-link>
    
    </div>
  </aside>
</template>

<script setup lang="ts">

</script>

<style scoped>
.sidebar {
  width: 70px;
  background: #1a1a1a;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 1rem;
  min-height: 100vh;
  border-right: 1px solid var(--dark-border);
}
.sidebar-menu {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  width: 100%;
  align-items: center;
}
.sidebar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #bfc8d8;
  font-size: 14px;
  cursor: pointer;
  padding: 0.5rem 0;
  border-radius: 12px;
  transition: background 0.2s, color 0.2s;
  width: 55px;
}
.sidebar-item.active {
  background: #2d3651;
  color: #5b8cff;
}

.sidebar-item:hover {
   background: #2d3651; /* hover 状态保持 */
  color: #5b8cff;
}

.sidebar-item .icon {
  font-size: 18px;
  margin-bottom: 0.25rem;
}
.sidebar-item span {
  font-size: 12px;
}
/* 移除折叠按钮样式 */
/* .collapse-btn {
  margin-top: auto;
  margin-bottom: 1rem;
  border: 1px solid #333;
  border-radius: 8px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
} */
</style> 