import http from '@/utils/http'

export interface ChildMenuItem {
  appMenuId: string
  appMenuName: string
  appMenuIcon: string
  appMenuExpanded: boolean
}

export interface MenuItem {
  appMenuId: string
  appMenuName: string
  appMenuIcon: string
  children?: ChildMenuItem[]
}

export interface AppMenuResponse {
  menu: MenuItem[]
}

export const appMenuApi = {
  getAppMenu: async (): Promise<AppMenuResponse> => {
    const response = await http.get<AppMenuResponse>('/api/app-menu')
    return response.data
  }
} 