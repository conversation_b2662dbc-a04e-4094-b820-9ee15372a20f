<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();

// 处理未授权事件
const handleUnauthorized = () => {
  // 清除用户信息
  document.cookie = 'user=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
  // 跳转到登录页
  router.push('/login');
};

onMounted(() => {
  // 添加未授权事件监听
  window.addEventListener('auth:unauthorized', handleUnauthorized);
});

onUnmounted(() => {
  // 移除事件监听
  window.removeEventListener('auth:unauthorized', handleUnauthorized);
});
</script>

<template>
  <router-view />
</template>

<style>
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --dark-primary: #1a1a1a;
  --dark-secondary: #1a1a1a;
  --dark-accent: #334155;
  --dark-text: #e2e8f0;
  --dark-text-secondary: #94a3b8;
  --dark-border: #47494c;
  --dark-input-bg: rgba(30, 41, 59, 0.5);
  --dark-card-bg: rgba(30, 41, 59, 0.8);
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: var(--dark-primary);
  color: var(--dark-text);
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--dark-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--dark-accent);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #475569;
}

/* 输入框自动填充样式 */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus {
  -webkit-text-fill-color: var(--dark-text);
  -webkit-box-shadow: 0 0 0px 1000px var(--dark-input-bg) inset;
  transition: background-color 5000s ease-in-out 0s;
}

/* 选中文本样式 */
::selection {
  background: var(--dark-accent);
  color: var(--dark-text);
}
</style>
