<template>
  <div class="dialog-mask" v-if="visible">
    <div class="dialog-content">
      <div class="dialog-header">
        <div class="dialog-title-row">
          <i class="fas fa-layer-group dialog-title-icon"></i>
          <div class="dialog-title-group">
            <span class="dialog-title">选择知识库</span>
            <span class="dialog-desc">仅能选择同一个索引模型的知识库</span>
          </div>
        </div>
        <button class="dialog-close-btn" @click="close" aria-label="关闭">&times;</button>
      </div>
      <div class="kb-card-list selected-list" v-if="selectedList.length">
        <div
          v-for="kb in selectedList"
          :key="kb.kbId"
          class="kb-card selected"
        >
          <div class="kb-info">
            <div class="kb-top-row">
              <div class="kb-icon" :style="{ background: getKbInfo(kb.kbId)?.kbBg || '#232c3a' }">
                <i :class="getKbInfo(kb.kbId)?.kbIcon || 'fas fa-book'"></i>
              </div>
              <div class="kb-name">{{ kb.kbName }}</div>
              <span class="kb-remove" @click.stop="removeSelected(kb)">
                <i class="fas fa-trash"></i>
              </span>
            </div>
          </div>
        </div>
      </div>
      <hr class="kb-divider" v-if="selectedList.length && unselectedList.length" />
      <div class="kb-card-list">
        <div
          v-for="kb in unselectedList"
          :key="kb.kbId"
          class="kb-card"
          @click="selectKb(kb)"
        >
          <div class="kb-info">
            <div class="kb-top-row">
              <div class="kb-icon" :style="{ background: kb.kbBg }">
                <i :class="kb.kbIcon"></i>
              </div>
              <div class="kb-name">{{ kb.kbName }}</div>
            </div>
            <div class="kb-model">
              <i class="fas fa-link"></i>
              {{ kb.embeddingModel }}
            </div>
          </div>
        </div>
      </div>
      <div class="dialog-footer">
        <button class="confirm-btn" @click="confirm">完成</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, onMounted, computed, watch } from 'vue'
import { getKnowledgeBaseSimpleList, type KnowledgeBaseSimpleItem } from '@/api/kb'
import { cookieUtils } from '@/utils/cookie'
import { useRouter, useRoute } from 'vue-router'
// 简化的知识库类型
interface SimpleKB {
  kbId: string
  kbName: string
}

const props = defineProps<{ 
  visible: boolean,
  selectedKB?: SimpleKB[] 
}>()
const emit = defineEmits(['close', 'select'])

const router = useRouter()
const username = ref<string>('')
const route = useRoute()

const knowledgeBases = ref<KnowledgeBaseSimpleItem[]>([])
// 使用props.selectedKB作为默认值，如果没有则使用空数组
const selectedKb = ref<SimpleKB[]>(props.selectedKB || [])

const selectedList = selectedKb
const unselectedList = computed(() =>
  knowledgeBases.value.filter(
    kb => !selectedKb.value.some(sel => sel.kbId === kb.kbId)
  )
)

onMounted(async () => {
  const userCookie = cookieUtils.getCookie('user');
  if (userCookie) {
    try {
      const userObj = JSON.parse(userCookie);
      username.value = userObj.username;
    } catch (e) {
      console.error('解析user cookie失败', e);
    }
  }

  try {
    const res = await getKnowledgeBaseSimpleList(username.value)
    knowledgeBases.value = res.items
  } catch (err: any) {
    if (err?.response?.status === 401) {
      router.push('/login')
    } else {
      console.error('获取知识库列表失败', err)
    }
  }
})

function close() {
  emit('close')
}

function selectKb(kb: KnowledgeBaseSimpleItem) {
  if (!selectedKb.value.some(sel => sel.kbId === kb.kbId)) {
    selectedKb.value.push({
      kbId: kb.kbId,
      kbName: kb.kbName
    })
  }
}

function removeSelected(kb: SimpleKB) {
  selectedKb.value = selectedKb.value.filter(sel => sel.kbId !== kb.kbId)
}

async function confirm() {
      // 更新成功后发送事件
  emit('select', selectedKb.value)
  close()
}

// 获取知识库的完整信息
function getKbInfo(kbId: string) {
  return knowledgeBases.value.find(kb => kb.kbId === kbId)
}


watch(
  () => props.selectedKB,
  (val) => {
    if (val) {
      selectedKb.value = val
    }
  },
  { immediate: true, deep: true }
)

</script>

<style scoped>
.dialog-mask {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.45);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;

}
.dialog-content {
  background: #23242a;
  border-radius: 12px;
  width: 60vw;
  height: 70vh;
  min-width: 400px;
  min-height: 300px;
  max-width: 900px;
  max-height: 80vh;
  box-shadow: 0 2px 24px #000a;
  display: flex;
  flex-direction: column;
}
.dialog-header {
  position: relative;
  padding: 10px 32px 10px 20px;
  border-bottom: 1px solid #333;
  background-color: #26282e;
  border-radius: 12px 12px 0 0;
}
.dialog-title-row {
  display: flex;
  align-items: center;
  gap: 12px;
}
.dialog-title-icon {
  font-size: 1.7rem;
  color: #6a6ff7;
  flex-shrink: 0;
}
.dialog-title-group {
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.dialog-title {
  font-size: 0.9rem;
  font-weight: 500;
  color: #e3e5ec;
}
.dialog-desc {
  color: #8b95a5;
  font-size: 0.8rem;
  margin-top: 2px;
}
.dialog-close-btn {
  position: absolute;
  top: 18px;
  right: 24px;
  background: transparent;
  border: none;
  font-size: 1.7rem;
  color: #8b95a5;
  cursor: pointer;
  transition: color 0.2s;
  line-height: 1;
}
.dialog-close-btn:hover {
  color: #fff;
}
.kb-card-list {
  overflow-y: auto;
  display: flex;
  flex-wrap: wrap;
  gap: 18px;
  padding: 15px 32px 0 32px;
}
.kb-card {
  background: #25262b;
  border: 2px solid transparent;
  border-radius: 12px;
  width: 220px;
  min-height: 55px;
  display: flex;
  align-items: center;
  gap: 16px;
  cursor: pointer;
  transition: border 0.2s, box-shadow 0.2s, background 0.2s;
  box-shadow: 0 2px 8px #0003;
  padding: 15px 12px 8px 12px;
  position: relative;
  border: 1px solid #484a4d;
}
.kb-card.selected {
  border: 1px solid #6a6ff7;
  background: #232c3a;
  box-shadow: 0 4px 16px #6a6ff733;
}
.kb-icon {
  width: 30px;
  height: 30px;
  background: #232c3a;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.4rem;
  color: #6a6ff7;
}
.kb-icon i {
  font-size: 1rem;
  color: #e7e7ef;
}
.kb-info {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 0;
}
.kb-top-row {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}
.kb-name {
  font-size: 1rem;
  font-weight: 500;
  color: #e3e5ec;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.kb-model {
  font-size: 0.92rem;
  color: #6a6ff7;
  display: flex;
  align-items: center;
  gap: 6px;
  justify-content: flex-end;
  margin-top: 4px;
}
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  padding: 0 32px;
  margin-top: auto;
  margin-bottom: 20px;
}
.confirm-btn {
  background: linear-gradient(90deg, #6a6ff7 0%, #6fd0ff 100%);
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 10px 32px;
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.2s;
}
.confirm-btn:disabled {
  background: #444;
  color: #aaa;
  cursor: not-allowed;
}
.kb-card-list.selected-list {
  margin-bottom: 4px;
}
.kb-remove {
  margin-left: 8px;
  color: #a89c9c;
  cursor: pointer;
  font-size: 1rem;
  transition: color 0.2s;
}
.kb-remove:hover {
  color: #ff7875;
}
.kb-divider {
  border: none;
  border-top: 1px solid #333;
  margin: 12px 0;
}
</style> 