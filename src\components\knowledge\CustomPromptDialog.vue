<template>
  <div class="custom-prompt-dialog">
    <div class="dialog-mask" @click="$emit('close')"></div>
    <div class="dialog-content">
      <div class="dialog-title">自定义提示词</div>
      <textarea v-model="localValue" class="dialog-textarea"></textarea>
      <div class="dialog-fixed">
        <pre>{{ fixedContent }}</pre>
      </div>
      <div class="dialog-footer">
        <button class="cancel-btn" @click="$emit('close')">取消</button>
        <button class="confirm-btn" @click="confirm">确认</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
const props = defineProps<{
  modelValue: string,
  fixedContent: string
}>()
const emit = defineEmits(['update:modelValue', 'close'])
const localValue = ref(props.modelValue)
watch(() => props.modelValue, v => localValue.value = v)
function confirm() {
  emit('update:modelValue', localValue.value)
  emit('close')
}
</script>

<style scoped>
.custom-prompt-dialog {
  position: fixed;
  z-index: 9999;
  left: 0; top: 0; right: 0; bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.dialog-mask {
  position: absolute;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.25);
}
.dialog-content {
  position: relative;
  background: #23242a;
  color: #f5f6fa;
  border-radius: 10px;
  padding: 12px 24px 20px 24px;
  min-width: 600px;
  max-width: 90vw;
  box-shadow: 0 4px 32px rgba(0,0,0,0.18);
  z-index: 1;
  display: flex;
  flex-direction: column;
}
.dialog-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 18px;
}
.dialog-textarea {
  width: 100%;
  min-height: 170px;
  background: #1e1f23;
  color: #f5f6fa;
  border: 1px solid #444;
  border-radius: 6px;
  padding: 12px;
  font-size: 12px;
  resize: vertical;
  outline: none;
}
.dialog-fixed {
  border-radius: 6px;
  padding-left: 12px;
  font-size: 14px;
  color: #8b95a5;
  white-space: pre-wrap;
  word-break: break-all;
}
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
.cancel-btn, .confirm-btn {
  padding: 6px 18px;
  border-radius: 6px;
  border: none;
  font-size: 15px;
  cursor: pointer;
}
.cancel-btn {
  background: #2a2b2f;
  color: #8b95a5;
}
.confirm-btn {
  background: #6a6ff7;
  color: #fff;
}
.confirm-btn:hover {
  background: #4cd6f8;
}
</style> 