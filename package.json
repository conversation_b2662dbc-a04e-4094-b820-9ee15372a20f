{"name": "chatbox", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@fortawesome/fontawesome-free": "^6.7.2", "@types/axios": "^0.9.36", "axios": "^1.9.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "pinia": "^3.0.2", "uuid": "^11.1.0", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "@types/node": "^22.15.21", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.17", "postcss": "^8.4.35", "postcss-nesting": "^12.0.2", "tailwindcss": "^3.4.1", "typescript": "~5.8.3", "vite": "^6.3.5", "vue-tsc": "^2.2.8"}}