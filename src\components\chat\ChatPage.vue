<template>
    <div class="chat-page-container">
      <!-- Left Sidebar - Application Navigation -->
      <div class="left-sidebar">
        <div class="sidebar-header" @click="handleExitChat">
          <i class="fas fa-arrow-left"></i>
          <span>退出聊天</span>
        </div>
        <div class="sidebar-section">
          <div class="section-title">最近使用</div>
          <div class="section-more">更多</div>
        </div>
        <!-- Application list -->
        <div
          class="app-item"
          v-for="app in sortedApps"
          :key="app.appId"
          :class="{ active: app.appId === activeAppId }"
          @click="handleAppClick(app)"
        >
          <i :class="[app.appIcon]"></i>
          <span>{{ app.appName }}</span>
        </div>
      </div>
      <!-- Right Main Content - Chat Area -->
      <router-view></router-view>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, computed, onMounted } from 'vue';
  import { useRouter, useRoute } from 'vue-router';
  import { useAppStore } from '@/store/app';
  import { chatApi } from '@/api/chat';
  import { cookieUtils } from '@/utils/cookie';
  import { ChatAppItem } from '@/api/chat';

  const router = useRouter();
  const route = useRoute();
  const appStore = useAppStore();

  const appList = ref<ChatAppItem[]>([]);

  // 按创建时间排序的应用列表
  const sortedApps = computed(() => {
    return [...(appList.value || [])].sort((a, b) => 
      new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
    );
  });

  const activeAppId = ref('');

  // 处理应用点击
  const handleAppClick = (app: ChatAppItem) => {
    activeAppId.value = app.appId;
    // 设置当前应用信息到 store
    appStore.setCurrentApp(app);
    router.push({
      name: 'ChatContent',
      query: { appId: app.appId }
    });
  };

  // 处理退出聊天
  const handleExitChat = () => {
    // 清除当前应
    // 用信息
    appStore.clearCurrentApp();
    router.push('/workflow/apps');
  };

  // 组件创建时自动导航到第一个应用
  onMounted(async () => {
    let username = '';
    const userCookie = cookieUtils.getCookie('user');
    if (userCookie) {
      try {
        const userObj = JSON.parse(userCookie);
        username = userObj.username || '';
      } catch (e) {
        username = '';
      }
    }
    try {
      const res = await chatApi.getUserApps(username);
        appList.value = Array.isArray(res) ? res : [];
        console.log("appList",appList.value)
    } catch (e) {
      appList.value = [];
    }
    const appIdRaw = route.query.appId;
    const appId = Array.isArray(appIdRaw) ? appIdRaw[0] : appIdRaw || '';
    if (!appId && appList.value.length > 0) {
      appStore.setCurrentApp(appList.value[0]);
      router.replace({
        name: 'ChatContent',
        query: { appId: appList.value[0].appId }
      });
      activeAppId.value = appList.value[0].appId;
    } else if (appId) {
      router.replace({
        name: 'ChatContent',
        query: { appId }
      });
      activeAppId.value = appId;
    }
  });
  </script>
  
  <style scoped>
  .chat-page-container {
    display: flex;
    height: 100vh;
    background-color: #1a1a1a; /* Dark background */
  }
  
  .left-sidebar {
    width: 200px;
    background-color: #23242a; /* Dark sidebar background */
    border-right: 1px solid #2d3651; /* Darker border */
    padding: 10px;
    display: flex;
    flex-direction: column;
  }
  
  .sidebar-header {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px;
    font-size: 1rem;
    font-weight: bold;
    color: #bfc8d8; /* Light text for dark theme */
    cursor: pointer;
    transition: color 0.2s;
    border-bottom: 1px solid #2d3651;
    padding-bottom: 20px;
  }
  
  .sidebar-header:hover {
    color: #5b8cff;
  }
  
  .sidebar-section {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px;
      font-size: 0.9rem;
      color: #bfc8d8;
      margin-top: 10px;
  }
  
  .section-title {
      font-weight: bold;
  }
  
  .section-more {
      color: #5b8cff;
      cursor: pointer;
      transition: color 0.2s;
  }
  
  .section-more:hover {
      color: #7fdcff;
  }
  
  .app-item {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 10px;
      margin-bottom: 5px;
      border-radius: 6px;
      cursor: pointer;
      color: #bfc8d8;
      transition: all 0.2s;
  }
  
  .app-item:hover, .app-item.active {
      background-color: #2d3651;
      color: #5b8cff;
  }
  
  .app-item i {
      color: #5b8cff;
  }
  </style> 