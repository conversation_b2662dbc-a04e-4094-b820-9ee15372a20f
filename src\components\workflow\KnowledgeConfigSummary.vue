<template>
  <div v-if="selectedKB && selectedKB.length" class="kb-config-summary-table">
    <div class="config-table">
      <div class="config-table-row config-table-header">
        <div>搜索方式</div>
        <div>引用上限</div>
        <div>最低相关度</div>
        <div>结果重排</div>
        <div>问题优化</div>
      </div>
      <div class="config-table-row config-table-value">
        <div class="icon-cell">
          <i class="fas fa-compass"></i>
          <span>{{ searchModeLabel }}</span>
        </div>
        <div>{{ config.quoteLimit }}</div>
        <div>{{ config.minScore }}</div>
        <div>
          <span class="custom-checkbox" :class="{ checked: config.rerankEnabled }">
            <input type="checkbox" disabled :checked="config.rerankEnabled" />
            <span class="checkmark"></span>
          </span>
        </div>
        <div>{{ config.optimizeEnabled ? config.optimizeModel : '-' }}</div>
      </div>
    </div>
    <div class="kb-list-row-table">
      <div v-for="kb in selectedKB" :key="kb.kbId" class="kb-card-table">
        <i class="fas fa-database"></i>
        <span>{{ kb.kbName }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps<{
  config: any,
  selectedKB: { kbId: string, kbName: string }[]
}>()

const searchModeLabel = computed(() => {
  const map: Record<string, string> = {
    semantic: '语义检索',
    fulltext: '全文检索',
    hybrid: '混合检索'
  }
  return map[props.config?.searchMode] || props.config?.searchMode || '-'
})
</script>

<style scoped>
.kb-config-summary-table {
  border-radius: 14px;
  margin-bottom: 12px;
  border-bottom: 1px solid #232c3a;
  padding-bottom: 12px;
}

.config-table {
  width: 100%;
  display: flex;
  flex-direction: column;
  margin-bottom: 12px;
}

.config-table-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.config-table-header {
  background: #232c3a;
  color: #b0b8c1;
  font-size: 12px;
  font-weight: 500;
  border-radius: 10px 10px 0 0;
  padding: 8px 0 6px 0;
}

.config-table-header > div {
  flex: 1;
  text-align: center;
}

.config-table-value {
  background: #23242a;
  color: #e2e8f0;
  font-size: 12px;
  border-radius: 0 0 10px 10px;
  padding: 10px 0 8px 0;
  border-bottom: 1px solid #232c3a;
}

.config-table-value > div {
  flex: 1;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.icon-cell i {
  color: #6a6ff7;
  font-size: 1.1em;
}

.kb-list-row-table {
  display: flex;
  gap: 16px;
  margin-top: 8px;
  justify-content: flex-start;
}

.kb-card-table {
  display: flex;
  align-items: center;
  background: #232c3a;
  border-radius: 8px;
  padding: 6px 18px;
  font-size: 15px;
  color: #7fdcff;
  border: 1.5px solid #232c3a;
  gap: 8px;
  box-shadow: 0 2px 8px #0008;
  transition: box-shadow 0.2s, border 0.2s;
}

.kb-card-table i {
  color: #6a6ff7;
  font-size: 1.1em;
}

.custom-checkbox {
  position: relative;
  display: inline-block;
  width: 14px;
  height: 14px;
  vertical-align: middle;
}
.custom-checkbox input[type="checkbox"] {
  opacity: 0;
  width: 14px;
  height: 14px;
  margin: 0;
  position: absolute;
  left: 0; top: 0;
  cursor: not-allowed;
}
.custom-checkbox .checkmark {
  position: absolute;
  left: 0; top: 0;
  width: 14px;
  height: 14px;
  border-radius: 3px;
  background: #232c3a;
  border: 2px solid #7fdcff;
  box-sizing: border-box;
  transition: background 0.2s, border 0.2s;
}
.custom-checkbox.checked .checkmark {
  background: #7fdcff;
  border-color: #7fdcff;
}
.custom-checkbox .checkmark:after {
  content: '';
  position: absolute;
  display: none;
}
.custom-checkbox.checked .checkmark:after {
  display: block;
  left: 3.5px;
  top: 0.5px;
  width: 4px;
  height: 7px;
  border: solid #23242a;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
  content: '';
}
</style> 