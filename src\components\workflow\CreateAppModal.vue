<template>
  <div class="modal-mask" @click.self="close">
    <div class="modal-container">
      <div class="modal-header">
        <div class="modal-icon" :style="{background: appTypeBg || 'linear-gradient(135deg, #7fdcff 0%, #6a6ff7 100%)'}">
          <i :class="appTypeIcon"></i>
        </div>
        <div class="modal-title">创建{{ appTypeName }}</div>
      </div>
      <div class="modal-body">
        <div class="input-label">取个名字</div>
        <div class="input-row">
          <div class="input-icon" :style="{background: appTypeBg || 'linear-gradient(135deg, #7fdcff 0%, #6a6ff7 100%)'}">
            <i :class="appTypeIcon"></i>
          </div>
          <input
            class="modal-input"
            :class="{ 'input-error': showError }"
            @blur="validate"
            v-model="inputValue"
            ref="nameInput"
          />
        </div>
        
        <div class="template-list">
          <div class="template-card" v-for="(tpl, index) in templates" :key="tpl.appTypeTemplateId" @click="handleCardClick(tpl)">
            <div class="template-header">
            <div class="template-icon" :style="{background: tpl.appBg}">
              <i :class="tpl.appIcon"></i>
              </div>
              <div class="template-title">{{ tpl.appTypeTemplateName }}</div>
            </div>
            <div class="template-info">
              <div class="template-desc" v-if="tpl.appTypeTemplateDesc">{{ tpl.appTypeTemplateDesc }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="modal-close-btn" @click="close">关闭</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { v4 as uuidv4 } from 'uuid'
import { appsApi, TemplateItem } from '@/api/apps'
import { cookieUtils } from '@/utils/cookie'

const props = defineProps<{ 
  appTypeId: string; 
  appTypeName: string;
  appTypeIcon?: string;
  appTypeBg?: string;
}>()
const emit = defineEmits(['close'])
function close() { emit('close') }

const templates = ref<TemplateItem[]>([])
const username = ref<string>('')

async function fetchTemplates() {
  if (!props.appTypeId) return;
  try {
    templates.value = await appsApi.getTemplates(props.appTypeId);
  } catch (e) {
    templates.value = [];
    console.error('获取模板失败', e);
  }
}

onMounted(() => {
    const userCookie = cookieUtils.getCookie('user');
    if (userCookie) {
      try {
        const userObj = JSON.parse(userCookie);
        username.value = userObj.username || '';
      } catch (e) {
        username.value = '';
      }
    }
    fetchTemplates()
  }
)
watch(() => props.appTypeId, fetchTemplates)

const nameInput = ref();

const inputValue = ref('');
const showError = ref(false);

const router = useRouter();

function validate() {
  if (!inputValue.value.trim()) {
    const input = nameInput.value;
    input?.classList.add('input-shake');
    setTimeout(() => {
      input?.classList.remove('input-shake');
    }, 50);
  }
}

async function handleCardClick(tpl: any) {
  if (!inputValue.value.trim()) {
    const input = nameInput.value;
    input?.classList.add('input-shake');
    setTimeout(() => {
      input?.classList.remove('input-shake');
    }, 50);
    nameInput.value?.focus();
    return;
  }
  // 跳转到新页面，带上唯一 appId
  const appId = uuidv4();
  
  
  const payload = {
    appId: appId,
    appName: inputValue.value,
    appTypeId: props.appTypeId,
    appTypeName: props.appTypeName,
    creatorId: username.value,
    appIcon: tpl.appIcon,
    appBg: tpl.appBg,
    appDesc: `这是一个基于${tpl.appTypeTemplateName}的对话应用示例。`,
  };

  try {
    const res = await appsApi.createApp(payload);

    router.push({
      path: '/app/detail',
      query: { appId: appId }
    });
  } catch (e) {
    console.error('创建应用失败', e);
  }
}

</script>

<style scoped>
.modal-mask {
  position: fixed;
  z-index: 999999;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(30, 30, 40, 0.55);
  display: flex;
  align-items: center;
  justify-content: center;
}
.modal-container {
  background: #23242a;
  border-radius: 1.2rem;
  box-shadow: 0 8px 48px #000a;
  width: 670px;
  max-width: 96vw;
  padding: 0 0 1.2rem 0;
  display: flex;
  flex-direction: column;
  position: relative;
}
.modal-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.7rem 2rem 0.5rem 2rem;
  border-radius: 1.2rem 1.2rem 0 0;
  position: relative;
  background: #2b2a2a;
  margin-bottom: 0.5rem;
}
.modal-icon {
  width: 2.5rem;
  height: 2.5rem;
  background: linear-gradient(135deg, #7fdcff 0%, #6a6ff7 100%);
  border-radius: 0.7rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 1.5rem;
}
.modal-title {
  font-size: 1.25rem;
  font-weight: bold;
  color: #fff;
}

.modal-body {
  padding: 0.5rem 2rem 0.5rem 2.5rem;
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
}
.input-row {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
}
.input-icon {
  width: 2.2rem;
  height: 2.2rem;
  background: linear-gradient(135deg, #7fdcff 0%, #6a6ff7 100%);
  border-radius: 0.6rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 1.2rem;
}
.modal-input {
  flex: 1;
  padding: 0.5rem 1rem;
  border-radius: 0.6rem;
  border: 1.5px solid #3b4252;
  background: #23242a;
  color: #fff;
  font-size: 1.1rem;
  outline: none;
  transition: border-color 0.2s;
}
.modal-input:focus {
  border-color: #7fdcff;
}
.section-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  font-size: 1rem;
  color: #b0b8c1;
  font-weight: bold;
}
.template-market-link {
  color: #7fdcff;
  font-size: 0.95rem;
  text-decoration: none;
  transition: color 0.2s;
}
.template-market-link:hover {
  color: #6a6ff7;
}
.template-list {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.2rem;
}
.template-card {
  background: #23242a;
  border-radius: 0.9rem;
  box-shadow: 0 2px 12px #0002;
  display: flex;
  flex-direction: column;
  padding: 1.1rem 1.2rem;
  cursor: pointer;
  transition: box-shadow 0.18s, background 0.18s;
  border: 1.5px solid #2a2a33;
}



.template-card {
  position: relative;
  padding-bottom: 2.5rem;
}

.template-card .template-icon {
  flex-shrink: 0;
}

.template-card .template-meta {
  position: absolute;
  bottom: 1rem;
  left: 1.2rem;
}

.template-card:hover {
  box-shadow: 0 4px 18px #7fdcff33;
  background: #232c3a;
  border-color: #7fdcff;
}

.template-header {
  display: flex;
  align-items: center;
  gap: 0.7rem;
  margin-bottom: 0.2rem;
}

.template-title {
  font-size: 1rem;
  font-weight: bold;
  color: #fff;
  white-space: nowrap;
}

.template-info {
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
  flex: 1;
}

.template-desc {
  font-size: 0.95rem;
  color: #b0b8c1;
  margin-bottom: 0.2rem;
}

.template-meta {
  font-size: 0.85rem;
  color: #7fdcff;
}
.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 0 2rem;
}
.modal-close-btn {
  padding: 0.5rem 1.5rem;
  background: linear-gradient(90deg, #6a6ff7 0%, #7fdcff 100%);
  color: #fff;
  border: none;
  border-radius: 0.8rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s, box-shadow 0.2s;
  box-shadow: 0 2px 12px #6a6ff733;
  letter-spacing: 0.02em;
}
.modal-close-btn:hover {
  background: linear-gradient(90deg, #7a7fff 0%, #7fdcff 100%);
  box-shadow: 0 4px 18px #6fd0ff55;
}
.input-label {
  font-size: 1.2rem;
  color: #fff;
  margin-bottom: -0.7rem;
  font-weight: 700;
  margin-top: 10px;

}
.template-icon {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  background: none;
  box-shadow: none;
  margin-right: 0.7rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: none;
  padding: 0;
}
.template-icon i {
  font-size: 1.5rem;
  color: #f0f3f5;
  filter: none;
}
.template-card:hover .template-icon {
  box-shadow: none;
  background: none;
}
.input-error {
  border-color: #ff4d4f !important;
  transition: border-color 0.2s;
}
@keyframes shake {
  0%, 100% { border-color: #3b4252; }
  25% { border-color: #3b4252; opacity: 0.5; }
  75% { border-color: #3b4252; opacity: 0.5; }
}

.input-shake {
  animation: shake 0.5s ease-in-out;
}
</style> 