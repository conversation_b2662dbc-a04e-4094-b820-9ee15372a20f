<template>
  <div class="chat-input-area">
    <div class="input-wrapper">
      <textarea
        v-model="inputMessage"
        class="chat-input"
        placeholder="输入消息..."
        @keydown.enter.prevent="handleEnter"
        :disabled="isLoading"
        ref="inputRef"
      ></textarea>
      <div class="input-actions">
        <button 
          class="send-button" 
          @click="handleSend"
          :disabled="!canSend || isLoading"
        >
          <i class="fas" :class="isLoading ? 'fa-spinner fa-spin' : 'fa-paper-plane'"></i>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue';

const props = defineProps<{
  isLoading?: boolean;
}>();

const emit = defineEmits<{
  (e: 'send', message: string): void;
}>();

const inputMessage = ref('');
const inputRef = ref<HTMLTextAreaElement | null>(null);

// 是否可以发送消息
const canSend = computed(() => {
  return inputMessage.value.trim().length > 0;
});

// 处理回车发送
const handleEnter = (e: KeyboardEvent) => {
  if (e.shiftKey) {
    // Shift + Enter 换行
    return;
  }
  if (canSend.value) {
    handleSend();
  }
};

// 处理发送消息
const handleSend = () => {
  if (!canSend.value || props.isLoading) return;
  
  const message = inputMessage.value.trim();
  emit('send', message);
  inputMessage.value = '';
  
  // 发送后聚焦输入框
  nextTick(() => {
    inputRef.value?.focus();
  });
};

// 监听 isLoading 变化，当加载完成时聚焦输入框
watch(() => props.isLoading, (newValue) => {
  if (!newValue) {
    nextTick(() => {
      inputRef.value?.focus();
    });
  }
});
</script>

<style scoped>
.chat-input-area {
  padding: 15px 20px;
  background-color: #242527;
  display: flex;
  justify-content: center;  /* 水平居中 */
}

.input-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 80%;
}

.chat-input {
  min-height: 24px;
  max-height: 120px;
  height: 120px;
  padding: 12px 15px;
  padding-right: 100px;
  border: 1px solid #2d3651;
  border-radius: 8px;
  font-size: 1rem;
  background-color: #23242a;
  color: #bfc8d8;
  transition: all 0.2s;
  resize: none;
  line-height: 1.5;
}

.chat-input:focus {
  outline: none;
  border-color: #5b8cff;
  background-color: #2d3651;
  box-shadow: 0 0 0 2px rgba(91, 140, 255, 0.1);
}

.chat-input::placeholder {
  color: #8b95a5;
}

.chat-input:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.input-actions {
  position: absolute;
  right: 8px;
  bottom: 8px;
  display: flex;
  gap: 8px;
  align-items: center;
}


.send-button {
  background: linear-gradient(90deg, #5b8cff 0%, #7fdcff 100%);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  box-shadow: 0 2px 8px rgba(91, 140, 255, 0.2);
  width: 45px;
  height: 32px;
}

.send-button:hover:not(:disabled) {
  background: linear-gradient(90deg, #6a9cff 0%, #8fecff 100%);
  box-shadow: 0 4px 12px rgba(91, 140, 255, 0.3);
  transform: translateY(-1px);
}

.send-button:active:not(:disabled) {
  transform: translateY(0);
}

.send-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.send-button i {
  font-size: 1rem;
}
</style> 