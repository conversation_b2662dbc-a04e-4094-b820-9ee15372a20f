import { defineStore } from 'pinia';

export const useKnowledgeStore = defineStore('knowledge', {
  state: () => ({
    datasetInfo: {
      kbName: '',
      creatorId: '',
      kbTypeId: '',
      kbTypeName: '',
      kbIcon: '',
      kbBg: '',
      embeddingModel: '',
      chatModel: '',
      imageModel: '',
      kbDesc: '',
      accessLevel: '',
    }
  }),
  actions: {
    setDatasetInfo(info: Partial<typeof this.datasetInfo>) {
      this.datasetInfo = { ...this.datasetInfo, ...info };
    },
    clearDatasetInfo() {
      this.datasetInfo = {
        kbName: '',
        creatorId: '',
        kbTypeId: '',
        kbTypeName: '',
        kbIcon: '',
        kbBg: '',
        embeddingModel: '',
        chatModel: '',
        imageModel: '',
        kbDesc: '',
        accessLevel: ''
      };
    }
  }
}); 