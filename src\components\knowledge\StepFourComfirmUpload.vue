<template>
  <div class="confirm-upload-wrapper">
    <table class="file-table">
      <thead>
        <tr>
          <th>来源名</th>
          <th>状态</th>
          <th>操作</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="file in localFiles" :key="file.name">
          <td class="file-name-cell">
            <span class="file-icon">TXT</span>
            <span class="file-name">{{ file.name }}</span>
          </td>
          <td>
            <span class="status" :class="statusClass(file.name)">{{ fileStatus[file.name] || '等待中' }}</span>
          </td>
          <td>
            <button class="delete-btn" title="删除" @click="removeFile(file.name)" :disabled="uploading">
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="#8b95a5" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v2"></path><line x1="10" y1="11" x2="10" y2="17"></line><line x1="14" y1="11" x2="14" y2="17"></line></svg>
            </button>
          </td>
        </tr>
      </tbody>
    </table>
    <div class="footer-bar">
      <button class="upload-btn" :disabled="localFiles.length === 0 || uploading" @click="handleUpload">共{{ localFiles.length }}个文件 | 开始上传</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, defineEmits } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { batchUpdateFileStatus, deleteFile } from '@/api/kb'

const props = defineProps<{ files: { name: string, id: string }[] }>()
const emit = defineEmits(['update:files', 'upload-complete'])

const localFiles = ref([...props.files])
const uploading = ref(false)
const fileStatus = ref<Record<string, string>>({})

const router = useRouter()
const route = useRoute()
const kbId = route.query.kbId as string

watch(() => props.files, (val) => {
  localFiles.value = [...val]
})

async function handleUpload() {
  if (uploading.value || localFiles.value.length === 0) return
  uploading.value = true

  try {
    // 1. 状态全部设为"创建中"
    localFiles.value.forEach(f => fileStatus.value[f.name] = '创建中')

    // 2. 调用API更新所有文件的状态
    try {
      // 获取所有文件的ID
      const fileIds = localFiles.value.map(file => file.id)
      
      // 一次性更新所有文件状态
      await batchUpdateFileStatus({
        fileIds: fileIds,
        kbId: kbId
      })
      
      // 添加1秒延迟
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 更新所有文件状态为完成
      localFiles.value.forEach(f => fileStatus.value[f.name] = '完成')
    } catch (error) {
      console.error('Failed to update files status:', error)
      localFiles.value.forEach(f => fileStatus.value[f.name] = '失败')
    }

    // 3. 跳转到详情页
    router.push({ path: '/dataset/detail', query: { kbId: kbId } })
  } catch (error) {
    console.error('Upload failed:', error)
  } finally {
    uploading.value = false
  }
}

async function removeFile(fileName: string) {
  const file = localFiles.value.find(f => f.name === fileName)
  if (!file) return

  try {
    await deleteFile({
      fileId: file.id,
      kbId: kbId
    })
    
    const index = localFiles.value.findIndex(f => f.name === fileName)
    if (index > -1) {
      localFiles.value.splice(index, 1)
      emit('update:files', localFiles.value)
    }
  } catch (error) {
    console.error('Failed to delete file:', error)
  }
}

function statusClass(fileName: string) {
  const status = fileStatus.value[fileName]
  switch (status) {
    case '创建中':
      return 'status-creating'
    case '完成':
      return 'status-complete'
    case '失败':
      return 'status-error'
    default:
      return ''
  }
}
</script>

<style scoped>
.confirm-upload-wrapper {
  padding: 0 0 32px 0;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
  width: 100%;
  max-width: 100%;
  height: 100%;
  max-height: 100%;
  overflow: hidden;
}
.file-table {
  max-height: 90%;
  border-collapse: separate;
  border-spacing: 0;
  margin-bottom: 24px;
  margin: 12px 35px 12px 35px;
  border-radius: 12px;
  overflow-y: hidden;
}
.file-table thead {
  position: sticky;
  top: 0;
  z-index: 2;
  background: #2a2b2f;
}
.file-table tbody {
  display: block;
  max-height: calc(100vh - 330px);
  overflow-y: auto;
}
.file-table tr {
  display: table;
  width: 100%;
  table-layout: fixed;
}
.file-table th,
.file-table td {
  padding-left: 16px;
  padding-right: 8px;
  color: #f5f6fa;
}
.file-table th:first-child,
.file-table td:first-child {
  min-width: 120px;
  padding-left: 12px;
}
.file-table th {
  background: #2a2b2f;
  color: #f5f6fa;
  font-weight: 600;
  text-align: left;
  padding: 14px 0 14px 12px;
  font-size: 16px;
}
.file-table th + th, .file-table td + td {
  text-align: center;
}
.file-table td:last-child {
  text-align: center;
  vertical-align: middle;
}
.file-table td {
  background: #25262b;
  color: #f5f6fa;
  font-size: 15px;
  padding: 12px 0 12px 12px;
  border-bottom: 1px solid #2a2b2f;
  vertical-align: middle;
}
.file-name-cell {
  display: flex;
  align-items: center;
  gap: 10px;
}
.file-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background: #232f3b;
  color: #54bdbd;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 600;
  margin-right: 6px;
}
.status.waiting {
  display: inline-block;
  background: #232f3b;
  color: #8b95a5;
  border-radius: 8px;
  padding: 2px 16px;
  font-size: 14px;
}
.status.creating {
  background: #2a2b2f;
  color: #6fd0ff;
}
.status.done {
  background: #232f3b;
  color: #56dfdf;
}
.delete-btn {
  background: #232f3b;
  border: none;
  border-radius: 8px;
  padding: 6px 10px;
  cursor: pointer;
  transition: background 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}
.delete-btn:hover {
  background: #2a2b2f;
}
.footer-bar {
  display: flex;
  justify-content: flex-end;
  padding: 0 32px;
}
.upload-btn {
  background: linear-gradient(90deg, #6a6ff7 0%, #6fd0ff 100%);
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 10px 32px;
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.2s;
}
.upload-btn:hover {
  background: linear-gradient(90deg, #6e72e4 0%, #4cd6f8 100%);
}
</style> 