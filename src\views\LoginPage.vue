<template>
  <div class="login-container">
    <!-- 背景动画元素 -->
    <div class="background-animation">
      <div class="blob-container">
        <div class="blob blob-1"></div>
        <div class="blob blob-2"></div>
        <div class="blob blob-3"></div>
      </div>
    </div>

    <!-- 登录卡片 -->
    <div class="login-card">
      <div class="text-center">
        <h2 class="title">
          欢迎回来
        </h2>
        <p class="subtitle">
          请登录您的账号
        </p>
      </div>

      <form class="login-form" @submit.prevent="handleLogin">
        <div class="input-group">
          <div class="input-wrapper">
            <label for="phone" class="sr-only">手机号</label>
            <div class="input-container">
              <input
                id="phone"
                v-model="phone"
                name="phone"
                type="tel"
                pattern="[0-9]{11}"
                maxlength="11"
                required
                class="input"
                placeholder="请输入手机号"
                @input="validatePhone"
              />
              <div class="input-hover-effect"></div>
            </div>
            <div v-if="phoneError" class="error-message">
              {{ phoneError }}
            </div>
          </div>

          <div class="input-wrapper">
            <label for="password" class="sr-only">密码</label>
            <div class="input-container">
              <input
                id="password"
                v-model="password"
                name="password"
                type="password"
                required
                class="input"
                placeholder="密码"
              />
              <div class="input-hover-effect"></div>
            </div>
            <div v-if="errorMessage" class="error-message">
              {{ errorMessage }}
            </div>
          </div>
        </div>

        <div class="form-footer">
          <div class="remember-me">
            <input
              id="remember-me"
              name="remember-me"
              type="checkbox"
              class="checkbox"
            />
            <label for="remember-me" class="checkbox-label">
              记住我
            </label>
          </div>

          <div class="forgot-password">
            <a href="#" class="gradient-link">
              忘记密码?
            </a>
          </div>
        </div>

        <div>
          <button type="submit" class="login-button" @click="handleLogin">
            登录
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { userApi } from '@/api/user'
import { cookieUtils } from '@/utils/cookie'

const router = useRouter()
const phone = ref('')
const password = ref('')
const errorMessage = ref('')
const phoneError = ref('')

const validatePhone = () => {
  const phoneRegex = /^1[3-9]\d{9}$/
  if (!phone.value) {
    phoneError.value = ''
  } else if (!phoneRegex.test(phone.value)) {
    phoneError.value = '请输入正确的手机号'
  } else {
    phoneError.value = ''
  }
}

const validateForm = () => {
  // 验证手机号
  validatePhone()
  if (phoneError.value) {
    return false
  }

  // 验证密码
  if (!password.value) {
    errorMessage.value = '请输入密码'
    return false
  }

  return true
}

const handleLogin = async () => {
  if (!validateForm()) return

  try {
    errorMessage.value = '' // 清除之前的错误信息
    const response = await userApi.login({
      username: phone.value,
      password: password.value
    })
    // 保存用户信息和token到cookie
    cookieUtils.setCookie('user', JSON.stringify({
      ...response.user
    }))
  
    // 登录成功，跳转到首页
    router.push('/workflow/apps')
  } catch (error: any) {
    errorMessage.value = '登录失败，请检查账号密码'
  }
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(to bottom right, var(--dark-primary), var(--dark-secondary), var(--dark-accent));
  position: relative;
  overflow: hidden;
}

.background-animation {
  position: absolute;
  inset: 0;
  overflow: hidden;
}

.blob-container {
  position: absolute;
  inset: -10px;
  opacity: 0.5;
}

.blob {
  position: absolute;
  width: 18rem;
  height: 18rem;
  border-radius: 9999px;
  mix-blend-mode: multiply;
  filter: blur(24px);
  opacity: 0.7;
  animation: blob 7s infinite;
}

.blob-1 {
  top: 0;
  left: -1rem;
  background-color: #4f46e5;
}

.blob-2 {
  top: 0;
  right: -1rem;
  background-color: #0ea5e9;
  animation-delay: 2s;
}

.blob-3 {
  bottom: -2rem;
  left: 5rem;
  background-color: #7c3aed;
  animation-delay: 4s;
}

.login-card {
  max-width: 26rem;
  width: 100%;
  padding: 2rem;
  background-color: var(--dark-card-bg);
  backdrop-filter: blur(8px);
  border-radius: 1rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
  transform: translateZ(0);
  transition: transform 0.5s;
  padding-bottom: 4rem;
}

.login-card:hover {
  transform: scale(1.02);
}

.title {
  margin-top: 1.5rem;
  font-size: 2.25rem;
  font-weight: 800;
  text-align: center;
  background: linear-gradient(to right, #60a5fa, #a855f7);
  -webkit-background-clip: text;
  color: transparent;
}

.subtitle {
  margin-top: 0.5rem;
  font-size: 0.875rem;
  text-align: center;
  color: var(--dark-text-secondary);
}

.login-form {
  margin-top: 2rem;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.input-wrapper {
  position: relative;
}

.input-container {
  position: relative;
  z-index: 1;
}

.input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--dark-border);
  border-radius: 0.5rem;
  background-color: var(--dark-input-bg);
  color: var(--dark-text);
  transition: all 0.3s;
  position: relative;
  z-index: 2;
}

.input:focus {
  outline: none;
  border-color: transparent;
  box-shadow: 0 0 0 2px #3b82f6;
}

.input-hover-effect {
  position: absolute;
  inset: 0;
  border-radius: 0.5rem;
  transition: all 0.3s;
  z-index: 1;
  pointer-events: none;
}

.input-wrapper:hover .input-hover-effect {
  background: linear-gradient(to right, rgba(59, 130, 246, 0.1), rgba(168, 85, 247, 0.1));
}

.form-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 1.5rem;
}

.remember-me {
  display: flex;
  align-items: center;
}

.checkbox {
  width: 1rem;
  height: 1rem;
  border-radius: 0.25rem;
  border-color: var(--dark-border);
  background-color: var(--dark-input-bg);
  color: #3b82f6;
  transition: all 0.3s;
}

.checkbox:focus {
  box-shadow: 0 0 0 2px #3b82f6;
}

.checkbox-label {
  margin-left: 0.5rem;
  font-size: 0.875rem;
  color: var(--dark-text-secondary);
  transition: color 0.3s;
}

.checkbox-label:hover {
  color: var(--dark-text);
}

.forgot-password {
  font-size: 0.875rem;
}

.gradient-link {
  font-weight: 500;
  background: linear-gradient(to right, #60a5fa, #a855f7);
  -webkit-background-clip: text;
  color: transparent;
  transition: all 0.3s;
}

.gradient-link:hover {
  background: linear-gradient(to right, #3b82f6, #9333ea);
  -webkit-background-clip: text;
}

.login-button {
  position: relative;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.75rem 1rem;
  margin-top: 1.5rem;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: white;
  background: linear-gradient(to right, #3b82f6, #a855f7);
  transition: all 0.3s;
  transform: translateZ(0);
}

.login-button:hover {
  background: linear-gradient(to right, #2563eb, #9333ea);
  transform: scale(1.02);
}

.login-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px #3b82f6;
}

.button-icon {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  padding-left: 0.75rem;
}

.icon {
  width: 1.25rem;
  height: 1.25rem;
  color: #93c5fd;
  transition: color 0.3s;
}

.login-button:hover .icon {
  color: #bfdbfe;
}

.register-link,
.register-text {
  display: none;
}

.error-message {
  margin-top: 0.5rem;
  color: #ef4444;
  font-size: 0.875rem;
  text-align: left;
}

@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}
</style> 