<template>
  <div class="chat-main">
    <div class="chat-header">
      <div class="chat-tabs">
        <div class="chat-title" v-if="chatTitle">{{ chatTitle }}</div>
      </div>
    </div>
    <ChatMessage 
      :messages="chatMessages" 
      :app-id="appId"
      :active-chat-id="activeChatId"
      @send="handleSendMessage"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';
import ChatMessage from './ChatMessage.vue';
import { chatApi, type ChatMessage as ChatMsg, type ChatData, type ChatInfoItem } from '@/api/chat';
import { cookieUtils } from '@/utils/cookie';
import { useRouter } from 'vue-router';
const router = useRouter();
const emit = defineEmits<{
  (e: 'new-chat', newChatInfo: ChatInfoItem): void;
}>();

const props = defineProps<{
  appId: string;
  activeChatId?: string;
  activeChatTitle?: string;
}>();

const chatMessages = ref<ChatMsg[]>([]);
const chatTitle = ref('');
const username = ref('');

// 监听 activeApp 和 activeChatId 的变化
watch(
  [() => props.appId, () => props.activeChatId],
  async ([appId, chatId]) => {
    if (appId && chatId) {

      if (props.activeChatTitle){
        await fetchChatMessages(appId, chatId);
        chatTitle.value = props.activeChatTitle || '';
      }else {
        chatMessages.value = [];
        chatTitle.value = '';
      }
    } else {
      chatMessages.value = [];
      chatTitle.value = '';
    }
  },
  { immediate: true }
);

// 获取聊天消息
async function fetchChatMessages(appId: string, chatId: string) {
  try {
    if (!username.value) return;
    const data = await chatApi.getChatMessages(username.value, appId, chatId) as ChatData;
    chatMessages.value = data.messages || [];
  } catch (error) {
    console.error('Error fetching chat messages:', error);
    // TODO: 添加错误处理UI提示
  }
}

// 组件挂载时，如果已有 activeApp 和 activeChatId，则获取消息
onMounted(() => {
  const userCookie = cookieUtils.getCookie('user');
  if (userCookie) {
    try {
      const userObj = JSON.parse(userCookie);
      username.value = userObj.username || '';
    } catch (e) {
      username.value = '';
    }
  }
  if (props.appId && props.activeChatId) {
    fetchChatMessages(props.appId, props.activeChatId);
    chatTitle.value = props.activeChatTitle || '';
  }
});

// 处理发送消息
const handleSendMessage = (newChatInfo: ChatInfoItem) => {
  if (newChatInfo) {
    emit('new-chat', newChatInfo);
    chatTitle.value = newChatInfo.chatTitle;
  }
};
</script>

<style scoped>
.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #242527;
  height: 100%;
}

.chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px;
  background-color: #242527;
}

.chat-tabs {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  justify-content: center;
}

.chat-title {
  font-size: 1.1rem;
  font-weight: 500;
  color: #bfc8d8;
  padding: 6px 12px;
  border-radius: 6px;
}

.model-tag {
  display: flex;
  align-items: center;
  gap: 6px;
  background-color: #2d3651;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 0.85rem;
  color: #bfc8d8;
}

.chat-actions {
  color: #bfc8d8;
  cursor: pointer;
  font-size: 1.2rem;
  transition: color 0.2s;
  padding: 8px;
  border-radius: 6px;
}

.chat-actions:hover {
  color: #5b8cff;
  background-color: rgba(91, 140, 255, 0.1);
}

.chat-content-area {
  flex: 1;
  padding: 30px;
  overflow-y: auto;
  background-color: #23242a;
}

.initial-content-card {
  background-color: #1a1a1a;
  border-radius: 12px;
  padding: 25px;
  text-align: center;
  max-width: 500px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  border: 1px solid #2d3651;
}

.card-title {
  font-size: 1.2rem;
  font-weight: bold;
  margin-bottom: 25px;
  color: #bfc8d8;
}

.qrcode-section {
  margin-bottom: 20px;
}

.qrcode-title {
  font-size: 1.1rem;
  margin-bottom: 15px;
  color: #bfc8d8;
}

.qrcode-image {
  width: 220px;
  height: 220px;
  background-color: #2d3651;
  margin: 0 auto 20px auto;
  border-radius: 12px;
  border: 1px solid #3a3e48;
}

.qrcode-description {
  font-size: 0.95rem;
  color: #8b95a5;
  line-height: 1.6;
}

.chat-input-area {
  padding: 15px 20px;
  border-top: 1px solid #2d3651;
  display: flex;
  align-items: center;
  gap: 12px;
  background-color: #1a1a1a;
}

.chat-input {
  flex: 1;
  padding: 12px 15px;
  border: 1px solid #2d3651;
  border-radius: 8px;
  font-size: 1rem;
  background-color: #23242a;
  color: #bfc8d8;
  transition: all 0.2s;
}

.chat-input:focus {
  outline: none;
  border-color: #5b8cff;
  background-color: #2d3651;
  box-shadow: 0 0 0 2px rgba(91, 140, 255, 0.1);
}

.chat-input::placeholder {
  color: #8b95a5;
}

.send-button {
  background: linear-gradient(90deg, #5b8cff 0%, #7fdcff 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 18px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  box-shadow: 0 2px 8px rgba(91, 140, 255, 0.2);
}

.send-button:hover {
  background: linear-gradient(90deg, #6a9cff 0%, #8fecff 100%);
  box-shadow: 0 4px 12px rgba(91, 140, 255, 0.3);
  transform: translateY(-1px);
}

.send-button:active {
  transform: translateY(0);
}

.send-button i {
  font-size: 1.2rem;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
  background: #2d3651;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #3a3e48;
}
</style> 