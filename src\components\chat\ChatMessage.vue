<template>
  <div class="chat-message">
    <div class="message-list">
      <MessageSimpleChatItem 
        v-for="msg in localMessages" 
        :message="msg"
      />
    </div>
    <ChatInput 
      :is-loading="isLoading" 
      @send="handleSendMessage"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';
import ChatInput from './ChatInput.vue';
import MessageSimpleChatItem from './Message/MessageSimpleChatItem.vue';
import { v4 as uuidv4 } from 'uuid';
import { cookieUtils } from '@/utils/cookie';
import type { ChatInfoItem } from '@/api/chat';



interface DictItem {
  [key: string]: any;
}

export interface Message {
  role: 'user' | 'assistant';
  content: string;
  knowledge?: DictItem[];
  tags?: DictItem;
  subbutton?: [];
  metadata?: DictItem;
}

const props = defineProps<{
  messages?: Message[];
  appId?: string;
  activeChatId?: string;
}>();

const emit = defineEmits<{
  (e: 'send', newChatInfo: ChatInfoItem): void;
}>();

const isLoading = ref(false);
const chatId = ref('');

// 本地消息数组
const localMessages = ref<Message[]>([]);

const username = ref('');

// 监听props.messages变化，自动同步到localMessages（如父组件有更新）
watch(() => props.messages, (newVal) => {
  if (Array.isArray(newVal) && props.activeChatId) {
    chatId.value = props.activeChatId || '';
    localMessages.value = [...newVal];
  }
});


onMounted(() => {
// 组件初始化时获取username
  const userCookie = cookieUtils.getCookie('user');
  if (userCookie) {
    try {
      const userObj = JSON.parse(userCookie);
      username.value = userObj.username || '';
    } catch (e) {
      username.value = '';
    }
  }
})

// 调用大模型获取回复
const callLLM = async (message: string, appId?: string, chatId?: string): Promise<Message> => {
  try {
    // 直接用外部username
    // 先创建assistant消息并插入localMessages
    const assistantMsg: Message = { role: 'assistant', content: '',  'knowledge': [],  'tags': {}, 'subbutton': [], 'metadata': {} };
    let assistantMsgPushed = false; // 新增标记
    // 流式请求
    console.log('username', username.value)
    const response = await fetch('/api/chat-completion', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ message, appId, chatId, username: username.value }),
      credentials: 'include'
    });
    if (!response.body) throw new Error('No response body');  
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      buffer += decoder.decode(value, { stream: true });
      let lines = buffer.split('\n');
      buffer = lines.pop()!; // 最后一行可能是不完整的，留到下次
      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const jsonStr = line.slice(6).trim();
          if (jsonStr === '[DONE]') continue;
          
          const data = JSON.parse(jsonStr);
          // content 流式拼接
          assistantMsg.content += data.content;
          // 其它字段只要有就赋值（只会在最开始的chunk里有）
          ['knowledge', 'tags', 'subbutton', 'metadata'].forEach(key => {
            if (data[key] !== undefined) {
              (assistantMsg as any)[key] = data[key];
            }
          });
          if (!assistantMsgPushed) {
            localMessages.value = [...localMessages.value, assistantMsg];
            assistantMsgPushed = true;
          } else {
            // 只更新最后一条，并用新数组替换，确保响应式
            const updated = [...localMessages.value];
            updated[updated.length - 1] = { ...assistantMsg };
            localMessages.value = updated;
          }
        }
      }
    }
    return assistantMsg;
  } catch (error) {
    console.error('Error calling LLM:', error);
    throw error;
  }
};

// 处理发送消息
const handleSendMessage = async (message: string) => {
  if ((!localMessages.value || localMessages.value.length === 0)) {
    isLoading.value = true;
    
    try {
      if (!props.activeChatId) {
        chatId.value = uuidv4()
      }else{
        chatId.value = props.activeChatId || '';
      }
      const date = new Date();
      const formattedDate = date.toISOString().replace('T', ' ').split('.')[0].replace(' ', 'T');
      const newChatInfo: ChatInfoItem = {
        chatId: chatId.value,
        chatTitle: message.slice(0, 10) + (message.length > 10 ? '...' : ''),
        updatedAt: formattedDate
      };
      emit('send', newChatInfo);
      // 先push用户消息
      localMessages.value.push({ role: 'user', content: message });
      // 再请求LLM流式回复
      console.log('activelocalMessages', localMessages.value)
      await callLLM(message, props.appId, chatId.value);

   
    } catch (error) {
      console.error('Error in new chat:', error);
    } finally {
      isLoading.value = false;
    }
  } else {
    isLoading.value = true;
    try {
      // 先push用户消息
      localMessages.value.push({ role: 'user', content: message });
      console.log('localMessages', localMessages.value)
      // 再请求LLM流式回复
      await callLLM(message, props.appId, chatId.value);

    } catch (error) {
      console.error('Error in existing chat:', error);
    } finally {
      isLoading.value = false;
    }
  }
};

// 暴露方法给父组件
defineExpose({
  setLoading: (value: boolean) => {
    isLoading.value = value;
  }
});
</script>

<style scoped>
.chat-message {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #242527;
  height: 100%;
  overflow: hidden; /* 防止内容溢出 */
}

.message-list {
  flex: 1;  /* 让消息列表占据剩余空间 */
  padding: 10px 40px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow-y: auto;  /* 消息列表可滚动 */
}

.message-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  background-color: #1a1a1a;
  max-width: 85%;
}

.message-user {
  margin-left: auto;
  flex-direction: row-reverse;
  background-color: #2d3651;
}

.message-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: linear-gradient(135deg, #5b8cff 0%, #7fdcff 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.message-user .message-avatar {
  background: linear-gradient(135deg, #7fdcff 0%, #5b8cff 100%);
}

.message-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.message-text {
  color: #bfc8d8;
  font-size: 0.95rem;
  line-height: 1.5;
  white-space: pre-wrap;
}

.message-time {
  font-size: 0.8rem;
  color: #8b95a5;
}

.message-user .message-content {
  align-items: flex-end;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
  background: #2d3651;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #3a3e48;
}
</style> 