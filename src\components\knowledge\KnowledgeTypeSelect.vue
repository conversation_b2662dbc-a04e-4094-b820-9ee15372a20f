<template>
    <div class="type-select-popup">
      <div class="type-item" v-for="item in types" :key="item.kbTypeId" @click="$emit('select', item.kbTypeId, item.kbTypeName, item.kbIcon, item.kbBg)">
        <div class="type-icon" :style="{background: item.kbBg}">
          <i :class="item.kbIcon"></i>
        </div>
        <div class="type-info">
          <div class="type-title">{{ item.kbTypeName }}</div>
          <div class="type-desc">{{ item.kbTypeDesc }}</div>
        </div>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import { getKnowledgeBaseTypes, KnowledgeBaseType } from '@/api/kb';

  const types = ref<KnowledgeBaseType[]>([]);

  onMounted(async () => {
    try {
      const res = await getKnowledgeBaseTypes();
      types.value = res;
    } catch (e) {
      types.value = [];
    }
  });
  </script>
  
  <style scoped>
  .type-select-popup {
    background: #23242a;
    border-radius: 16px;
    box-shadow: 0 4px 32px #000a;
    padding: 0.2rem 0;
    min-width: 280px;
    max-width: 280px;
    width: 280px;
    position: absolute;
    right: 0;
    z-index: 100;
  }
  .type-item {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    padding: 10px 15px;
    border-radius: 12px;
    cursor: pointer;
    transition: background 0.18s;
  }
  .type-item:hover {
    background: #2d3651;
  }
  .type-icon {
    width: 40px;
    height: 40px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 1.6rem;
    padding: 0.5rem;
  }
  .type-title {
    font-weight: bold;
    color: #e6eaf3;
    font-size: 0.9rem;
  }
  .type-desc {
    color: #8b95a5;
    font-size: 0.7rem;
    margin-top: 4px;
  }
  </style>