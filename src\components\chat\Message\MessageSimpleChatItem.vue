<template>
  <div>
    <!-- 用户消息 -->
    <div v-if="message.role === 'user'" class="message-item message-user" ref="messageItemRef">
      <div class="message-content">
        <div class="message-text">{{ message.content }}</div>
      </div>
      <div class="message-avatar">
        <i class="fas fa-user"></i>
      </div>
    </div>
    <!-- 助手消息 -->
    <div v-else-if="message.role === 'assistant'" class="message-item message-assistant" ref="messageItemRef">
      <div class="message-avatar">
        <i class="fas fa-robot"></i>
      </div>
      <div class="message-content">
        <div v-if="message.knowledge?.length" class="message-knowledge">
          <div class="knowledge-header">
            <div class="knowledge-title">参考知识库：</div>
            <button class="collapse-button" @click="toggleKnowledge">
              <i class="fas" :class="isKnowledgeCollapsed ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
            </button>
          </div>
          <div v-show="!isKnowledgeCollapsed" class="knowledge-items-list">
            <div v-for="(item, index) in message.knowledge" :key="index" class="knowledge-item">
              <!-- TODO: 根据实际知识库数据结构显示内容 -->
              {{ JSON.stringify(item) }}
            </div>
          </div>
        </div>
        <div class="message-text">{{ message.content }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, watch } from 'vue';
import type { Message } from '../ChatMessage.vue';

const props = defineProps<{
  message: Message;
}>();

const isKnowledgeCollapsed = ref(true);
const messageItemRef = ref<HTMLElement | null>(null);

const toggleKnowledge = () => {
  isKnowledgeCollapsed.value = !isKnowledgeCollapsed.value;
};

// 滚动到消息项
const scrollIntoView = async () => {
  await nextTick();
  if (messageItemRef.value) {
    messageItemRef.value.scrollIntoView({ behavior: 'smooth', block: 'end' });
  }
};

// 监听消息内容变化
watch(
  () => props.message.content,
  async () => {
    await scrollIntoView();
  },
  { immediate: true }
);

// 监听知识库展开/收起状态变化
watch(
  () => isKnowledgeCollapsed.value,
  async () => {
    await scrollIntoView();
  }
);
</script>

<style scoped>
.message-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  max-width: 85%;
  scroll-margin-bottom: 20px; /* 添加滚动边距 */
}

.message-user {
  margin-left: auto;
  justify-content: flex-end;
  /* 让内容宽度自适应 */
  width: fit-content;
  max-width: 85%;
  margin-right: 5%;
}
.message-assistant {
  margin-right: auto;
  justify-content: flex-start;
  margin-left: 5%;
}
.message-user .message-content {
  align-items: flex-end;
  background-color: #2d3651;
  /* 让内容宽度自适应 */
  width: fit-content;
  max-width: 600px;
  min-width: 40px;
  padding: 12px 16px;
  border-radius: 8px;
  color: #fff;
  box-shadow: 0 2px 8px rgba(91, 140, 255, 0.08);
  word-break: break-word;
}

.message-user .message-avatar {
  background: linear-gradient(135deg, #7fdcff 0%, #5b8cff 100%);
}


.message-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: linear-gradient(135deg, #5b8cff 0%, #7fdcff 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.message-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

/* 限制AI回答内容宽度 */
.message-item:not(.message-user) .message-content {
  max-width: 800px;
  width: 100%;
  background-color: #1f2937;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(91, 140, 255, 0.1);
}

.message-text {
  color: #bfc8d8;
  font-size: 0.95rem;
  line-height: 1.5;
  white-space: pre-wrap;
}

.message-knowledge {
  margin-bottom: 12px;
  padding: 0 12px;
  background-color: rgba(91, 140, 255, 0.08);
  border-radius: 8px;
  font-size: 0.9rem;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(91, 140, 255, 0.15);
}

.knowledge-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.knowledge-title {
  color: #5b8cff;
  font-weight: 500;
  margin: 0;
  font-size: 0.95rem;
  display: flex;
  align-items: center;
  height: 100%;
}

.collapse-button {
  background: none;
  border: none;
  color: #5b8cff;
  cursor: pointer;
  font-size: 0.8rem;
  transition: transform 0.2s;
  padding: 4px;
  border-radius: 4px;
}

.collapse-button:hover {
  background-color: rgba(91, 140, 255, 0.1);
}

.collapse-button i {
  transform: rotate(0deg);
}

.collapse-button i.fa-chevron-up {
  transform: rotate(180deg);
}

.knowledge-items-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.knowledge-item {
  color: #8b95a5;
  padding: 8px;
  border-radius: 6px;
  background-color: rgba(91, 140, 255, 0.05);
  border: 1px solid rgba(91, 140, 255, 0.1);
}

.knowledge-item:first-child {
  border-top: none;
}

.message-user .message-content {
  align-items: flex-end;
  background-color: #2d3651;
}
</style> 