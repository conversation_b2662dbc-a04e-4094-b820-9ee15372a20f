import http from '@/utils/http'

interface LoginParams {
  username: string
  password: string
}

interface UserInfo {
  username: string
  nickname: string
  role: string
}

interface LoginResponse {
  message: string
  user: UserInfo
}

export const userApi = {
  login: async (params: LoginParams): Promise<LoginResponse> => {
    const formData = new URLSearchParams()
    formData.append('username', params.username)
    formData.append('password', params.password)
    const response = await http.post<LoginResponse>('/api/login', formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    })
    return response.data
  },

  logout: async (): Promise<void> => {
    await http.post('/api/logout')
  },

  getUserInfo: async () => {
    const response = await http.get('/api/user/info')
    return response.data
  }
} 