import http from '@/utils/http'

export interface AppItem {
  appId: string;
  appName: string;
  appDesc: string;
  appTypeId: string;
  appTypeName: string;
  creatorId: string;
  creatorName: string;
  appStatus: string;
  accessLevel: string;
  appPrivacy: string;
  updatedAt: string;
  appIcon: string;
  appBg: string;
  
  
}

export interface UserAppsResponse {
  apps: AppItem[];
}

export interface AppTypeItem {
  appTypeId: string;
  appTypeName: string;
  appTypeDesc: string;
  appTypeIcon: string;
  appTypeBg: string;
}

export interface AppTypeListResponse {
  items: AppTypeItem[];
}

export interface TemplateItem {
  appTypeTemplateId: number;
  appTypeTemplateName: string;
  appIcon: string;
  appBg: string;
  appTypeTemplateDesc: string;
}

export interface TemplatesResponse {
  appTemplates: TemplateItem[];
}

export interface KnowledgeBase {
  kbId: string;
  kbName: string;
}

export interface Tool { 
  toolId: string;
  toolName: string;
}

export interface FileUpload {
  fileId: string;
  fileName: string;
}
export interface AppCreateRequest {
  appId: string
  appName: string
  appTypeId: string
  appTypeName: string
  creatorId: string
  appIcon: string
  appBg: string
  appDesc: string
}

export interface AppCreateResponse {
  success: boolean
  message: string
}

export interface RerankModelInfo {
  modelName: string;
}

export interface RerankModelsResponse {
  models: RerankModelInfo[];
}

export const appsApi = {
  getUserApps: async (username: string): Promise<UserAppsResponse> => {
    const response = await http.get<UserAppsResponse>('/api/user-apps', { params: { username } });
    return response.data;
  },
  getAppTypes: async (): Promise<AppTypeItem[]> => {
    const response = await http.get<AppTypeListResponse>('/api/app-types');
    return response.data.items;
  },
  getTemplates: async (appTypeId: string): Promise<TemplateItem[]> => {
    const response = await http.get<TemplatesResponse>('/api/app-type-templates', { params: { appTypeId: appTypeId } });
    return response.data.appTemplates;
  },
  createApp: async (data: AppCreateRequest): Promise<AppCreateResponse> => {
    console.log(data);
    const response = await http.post<AppCreateResponse>('/api/create-app', data);
    return response.data;
  },
  updateAppKnowledge: async (data: { appId: string, username: string, selectedKB: { kbId: string, kbName: string }[] }): Promise<AppCreateResponse> => {
    const response = await http.post<AppCreateResponse>('/api/update-app-knowledge', data);
    return response.data;
  },
  updateAppKnowledgeSettings: async (data: { appId: string, username: string, knowledgeSettingsConfig: any }) => {
    const response = await http.post('/api/update-app-knowledge-settings', data)
    return response.data
  },
  getRerankModels: async (): Promise<RerankModelInfo[]> => {
    const response = await http.get<RerankModelsResponse>('/api/rerank-models');
    return response.data.models;
  }
}
