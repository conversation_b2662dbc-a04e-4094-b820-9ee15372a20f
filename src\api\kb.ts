import http from '@/utils/http';

export interface KnowledgeBaseItem {
  kbId: string;
  kbName: string;
  creatorId: string;
  creatorName: string;
  kbDesc: string;
  kbTypeId: string;
  kbTypeName: string;
  kbIcon: string;
  kbBg: string;
  embeddingModel: string;
  chatModel: string;
  imageModel: string;
  kbPrivate: string;
  accessLevel: string;
  updatedAt: string;
}

export interface KnowledgeBaseSimpleItem {
  kbId: string;
  kbName: string;
  embeddingModel: string;
  kbIcon: string;
  kbBg: string;
}

export interface KnowledgeBaseListResponse {
  items: KnowledgeBaseItem[];
}

export interface KnowledgeBaseSimpleListResponse {
  items: KnowledgeBaseSimpleItem[];
}

export interface KnowledgeBaseType {
  kbTypeId: string;
  kbTypeName: string;
  kbIcon: string;
  kbBg: string;
  kbTypeDesc: string;
}

export interface ModelOptionsResponse {
  chatModels: string[];
  embeddingModels: string[];
  imageModels: string[];
}

export interface CreateKnowledgeBaseRequest {
  kbId: string;
  kbName: string;
  creatorId: string;
  kbTypeId: string;
  kbTypeName: string;
  kbIcon: string;
  kbBg: string;
  embeddingModel: string;
  chatModel: string;
  imageModel: string;
  kbDesc: string;
  kbPrivate: string;
  accessLevel: string;
}

export interface CreateKnowledgeBaseResponse {
  success: boolean;
  message: string;
}

export interface FileInfo {
  fileId: string;
  fileName: string;
  fileProcessMethod: string;
  fileSplitNum: number;
  createdAt: string;
  updatedAt: string;
  fileStatus: string;
  isUse: boolean;
}

export interface KBFileListResponse {
  files: FileInfo[];
}

export interface UpdateFileStatusRequest {
  fileId: string;
  isUse: boolean;
}

export interface UpdateFileStatusResponse {
  success: boolean;
  message: string;
}

export interface UpdateModelRequest {
  kbId: string;
  modelType: 'embedding' | 'chat' | 'image';
  modelName: string;
}

export interface UploadFileRequest {
  kbId: string;
  file: File;
  fileProcessMethod: string;  // 处理方式，比如 'text', 'pdf' 等
}

export interface UploadFileResponse {
  message: string;
  fileId: string;
}

export interface DeleteFileRequest {
  fileId: string;
  kbId: string;
}

export interface DeleteFileResponse {
  success: boolean;
  message: string;
}

export interface ImportSettingsRequest {
  kbId: string;
  fileIds: string[];
  settings: {
    [key: string]: any;
  };
}

export interface ImportSettingsResponse {
  success: boolean
  message: string;
}

export interface BatchUpdateFileStatusRequest {
  fileIds: string[];
  kbId: string;
}

export interface BatchUpdateFileStatusResponse {
  sussces: boolean;
  message: string;

}

export async function getKnowledgeBaseList(username: string): Promise<KnowledgeBaseListResponse> {
  const res = await http.get('/api/kb-list', { params: { username } });
  return res.data as KnowledgeBaseListResponse;
}

export async function getKnowledgeBaseSimpleList(username: string): Promise<KnowledgeBaseSimpleListResponse> {
  const res = await http.get('/api/kb-simple-list', { params: { username } });
  return res.data as KnowledgeBaseSimpleListResponse;
}

export async function getKnowledgeBaseTypes(): Promise<KnowledgeBaseType[]> {
  const res = await http.get('/api/kb-types');
  const data = res.data as { items: KnowledgeBaseType[] };
  return data.items;
}

export async function getModelOptions(): Promise<ModelOptionsResponse> {
  const res = await http.get('/api/model-options');
  return res.data as ModelOptionsResponse;
}

export async function createKnowledgeBase(data: CreateKnowledgeBaseRequest): Promise<CreateKnowledgeBaseResponse> {
  const res = await http.post('/api/create-kb', data);
  return res.data as CreateKnowledgeBaseResponse;
} 

export async function validateKb(kbId: string): Promise<{ exists: boolean; message: string }> {
  const res = await http.get('/api/validate-kb', { params: { kbId } });
  return res.data as { exists: boolean; message: string };
}

export async function getKBFiles(kbId: string): Promise<KBFileListResponse> {
  const res = await http.get('/api/kb-files', { params: { kbId } });
  return res.data as KBFileListResponse;
}

export async function updateFileStatus(data: UpdateFileStatusRequest): Promise<UpdateFileStatusResponse> {
  const res = await http.post('/api/update-file-status', data);
  return res.data as UpdateFileStatusResponse;
}

export const updateModel = async (data: UpdateModelRequest): Promise<void> => {
  const response = await http.post('/api/update-model', data);
  if (response.status !== 200) {
    throw new Error('Failed to update model');
  }
};

export const deleteFile = async (data: DeleteFileRequest): Promise<DeleteFileResponse> => {
  const response = await http.post('/api/delete-file', data);
  return response.data as DeleteFileResponse;
};

export const importSettings = async (data: ImportSettingsRequest): Promise<ImportSettingsResponse> => {
  const response = await http.post('/api/import-files-settings', data);
  return response.data as ImportSettingsResponse;
};

export async function batchUpdateFileStatus(data: BatchUpdateFileStatusRequest): Promise<BatchUpdateFileStatusResponse> {
  const res = await http.post('/api/update-files-status', data);
  return res.data as BatchUpdateFileStatusResponse;
}