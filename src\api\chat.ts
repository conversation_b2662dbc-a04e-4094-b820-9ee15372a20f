import http from '@/utils/http'

export interface ChatAppItem {
  appId: string;
  appName: string;
  appIcon: string;
  appBg: string;
  updatedAt: string;
}

export interface ChatInfoItem {
    chatId: string;
    chatTitle: string
    updatedAt: string
}

export interface UserChatAppsResponse {
  apps: ChatAppItem[];
}

export interface UserChatInfoItemResponse {
    chats: ChatInfoItem[];
}

export interface KnowledgeItem {
  // TODO: 定义知识库项的具体结构
  [key: string]: any;
}

export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  knowledge?: KnowledgeItem[];  // 只有 assistant 消息才有 knowledge
}

export interface ChatData {
  messages: ChatMessage[];
}

export const chatApi = {
  getUserApps: async (username: string): Promise<UserChatAppsResponse> => {
    const res = await http.get<UserChatAppsResponse>('/api/app-list', { params: { username } });
    return res.data;
  },
  getChatList: async (username: string, appId: string): Promise<UserChatInfoItemResponse> => {
    const res = await http.get<UserChatInfoItemResponse>('/api/chat-list', { params: { username, appId } });
    return res.data;
  },
  getChatMessages: async (username: string, appId: string, chatId: string) => {
    const res = await http.get('/api/chat-messages', { params: { username, appId, chatId } });
    return res.data;
  },
  validateApp: async (appId: string): Promise<{ exists: boolean; message: string }> => {
    const res = await http.get('/api/validate-app', { params: { appId } });
    return res.data as { exists: boolean; message: string };
  }
}
