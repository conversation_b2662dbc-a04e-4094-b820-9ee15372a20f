<template>
  <div class="chat-sidebar">
    <div class="sidebar-header">
      <div class="chat-title-group">
        <i :class="[activeApp?.appIcon || 'fas fa-cube']"></i>
        <span>{{ activeApp?.appName}}</span>
      </div>
      <button class="new-chat-btn" @click="handleNewChat">
        <i class="fas fa-plus"></i>
        <span>新建对话</span>
      </button>
    </div>
    <div class="conversation-list">
      <div 
        v-for="chat in conversations" 
        :key="chat.chatId"
        class="conversation-item"
        :class="{ active: chat.chatId === props.actives }"
        @click="handleChatSelect(chat)"
      >
        <div class="conversation-title">
          <i class="fas fa-comment"></i>
          <span>{{ chat.chatTitle }}</span>
        </div>
        <div class="conversation-time">{{ formatTime(chat.updatedAt) }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { useAppStore } from '@/store/app';
import { chatApi, ChatInfoItem } from '@/api/chat';
import { cookieUtils } from '@/utils/cookie';
import { v4 as uuidv4 } from 'uuid';
import { useRouter } from 'vue-router';
const router = useRouter();
const props = defineProps<{
  appId: string;
  actives: string;
}>();

const appStore = useAppStore();
// 使用 store 中的应用信息
const activeApp = computed(() => appStore.getCurrentApp);

const emit = defineEmits<{
  (e: 'select', chatId: string, chatTitle: string): void;
  (e: 'new', newChat: ChatInfoItem): void;
}>();

const conversations = ref<ChatInfoItem[]>([]);
const username = ref('');
// 添加新对话
const addConversation = (newChatInfo: ChatInfoItem) => {
  // 检查是否已存在
  const index = conversations.value.findIndex(chat => chat.chatId === newChatInfo.chatId);
  if (index !== -1) {
    // 如果已存在，更新该对话
    conversations.value[index] = newChatInfo;
  } else {
    // 如果不存在，添加新对话
    conversations.value.push(newChatInfo);
  }
  // 重新排序
  conversations.value.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());
};
// 暴露方法给父组件
defineExpose({
  addConversation,
});

// 处理选择对话
const handleChatSelect = (chat: ChatInfoItem) => {
  emit('select', chat.chatId, chat.chatTitle);
};

// 处理新建对话
const handleNewChat = () => {
  const chat_id = uuidv4();
  const newChat = { chatId: chat_id, chatTitle: '', updatedAt: '' };
  emit('new', newChat);
};

// 格式化时间
const formatTime = (timeStr: string) => {
  const date = new Date(timeStr);
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  // 今天的对话
  if (diff < 24 * 60 * 60 * 1000) {
    return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
  }
  // 昨天的对话
  if (diff < 48 * 60 * 60 * 1000) {
    return '昨天';
  }
  // 一周内的对话
  if (diff < 7 * 24 * 60 * 60 * 1000) {
    const days = Math.floor(diff / (24 * 60 * 60 * 1000));
    return `${days}天前`;
  }
  // 更早的对话
  return date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' });
};

// 组件创建时初始化 username
onMounted(() => {
  const userCookie = cookieUtils.getCookie('user');
  if (userCookie) {
    try {
      const userObj = JSON.parse(userCookie);
      username.value = userObj.username || '';
    } catch (e) {
      username.value = '';
    }
  }
});

// 监听 username 和 appId 变化，自动拉取 chatList
watch([
  username,
  () => props.appId
], async ([newUsername, newAppId]) => {
  if (newUsername && newAppId) {
    try {
      const chatRes = await chatApi.getChatList(newUsername, newAppId);
      // 对聊天列表按更新时间降序排序
      conversations.value = Array.isArray(chatRes) 
        ? chatRes.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
        : [];
    } catch (e) {
      conversations.value = [];
    }
  } else {
    conversations.value = [];
  }
}, { immediate: true });
</script>

<style scoped>
.chat-sidebar {
  width: 220px;
  background-color: #292b2ce0;
  border-right: 1px solid #2d3651;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 15px;
  border-bottom: 1px solid #2d3651;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.chat-title-group {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1.1rem;
  font-weight: bold;
  color: #bfc8d8;
  padding: 8px 0;
}
.fas{
  line-height: 0!important;
}

.chat-title-group i {
  color: #5b8cff;
  font-size: 1.5rem;
}

.new-chat-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px;
  background: linear-gradient(90deg, #5b8cff 0%, #7fdcff 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.95rem;
  transition: all 0.2s;
}

.new-chat-btn:hover {
  background: linear-gradient(90deg, #6a9cff 0%, #8fecff 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(91, 140, 255, 0.2);
}

.new-chat-btn:active {
  transform: translateY(0);
}

.conversation-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.conversation-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  color: #bfc8d8;
}

.conversation-item:hover {
  background-color: #2d3651;
}

.conversation-item.active {
  background-color: #2d3651;
  color: #5b8cff;
}

.conversation-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.95rem;
}

.conversation-title i {
  font-size: 1rem;
  color: #5b8cff;
}

.conversation-time {
  font-size: 0.85rem;
  color: #8b95a5;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
  background: #2d3651;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #3a3e48;
}
</style> 