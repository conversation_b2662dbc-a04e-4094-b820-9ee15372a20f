import axios from 'axios'
import { REQUEST_CONFIG } from '@/config/api.config'

const http = axios.create({
  timeout: REQUEST_CONFIG.timeout,
  headers: REQUEST_CONFIG.headers || {}, // 确保 headers 总是对象
  baseURL: import.meta.env.DEV ? '' : import.meta.env.VITE_API_BASE_URL,
  withCredentials: true
})

// 请求拦截器 - 添加 CSRF 保护
http.interceptors.request.use(config => {
  // 确保 headers 对象存在
  config.headers = config.headers || {}
  
  // 非 GET 请求添加 CSRF 保护
  if (config.method && config.method.toLowerCase() !== 'get') {
    const csrfToken = getCsrfToken()
    if (csrfToken) {
      config.headers['X-CSRF-Token'] = csrfToken
    }
  }

  return config
})

// 响应拦截器
http.interceptors.response.use(
  response => response,
  error => {
    if (error.response?.status === 401) {
      // 触发全局未授权事件
      window.dispatchEvent(new CustomEvent('auth:unauthorized'))
    }
    return Promise.reject(error)
  }
)

// 获取 CSRF Token
function getCsrfToken(): string | null {
  const cookie = document.cookie
    .split('; ')
    .find(row => row.startsWith('csrf_token='))
  
  return cookie ? cookie.split('=')[1] : null
}

export default http