<template>
    <div class="file-upload">
      <!-- Upload Area -->
      <div class="upload-area">
        <div
          class="upload-box"
          :class="{ 'drag-over': isDragging }"
          @dragenter.prevent="handleDragEnter"
          @dragleave.prevent="handleDragLeave"
          @dragover.prevent
          @drop.prevent="handleDrop"
          @click="triggerFileInput"
        >
          <input
            type="file"
            ref="fileInput"
            multiple
            @change="handleFileSelect"
            style="display: none"
            accept=".txt,.docx,.csv,.xlsx,.pdf,.md,.html,.pptx"
          />
          <i class="fas fa-cloud-upload-alt upload-icon"></i>
          <div class="upload-tip">点击或拖动文件到此处上传</div>
          <div class="upload-desc">
            支持 .txt, .docx, .csv, .xlsx, .pdf, .md, .html, .pptx 类型文件<br>
            最多支持 15 个文件 单个文件最大 100 MB
          </div>
        </div>
      </div>
  
      <!-- File List -->
      <div v-if="files.length > 0" class="file-list-container">
        <div class="file-list">
          <div class="file-list-header">
            <div class="col-name">文件名</div>
            <div class="col-progress">文件上传进度</div>
            <div class="col-size">文件大小</div>
            <div class="col-action">操作</div>
          </div>
          <div v-for="(file, index) in files" :key="index" class="file-item">
            <div class="col-name">
              <i class="fas fa-file file-icon"></i>
              <span class="file-name">{{ file.name }}</span>
            </div>
            <div class="col-progress">
              <div class="progress-wrapper">
                <div v-if="file.error" class="file-error">
                  {{ file.error }}
                </div>
                <template v-else>
                  <div class="progress-bar">
                    <div 
                      class="progress" 
                      :style="{ width: `${file.progress || 0}%` }"
                    />
                  </div>
                  <span class="progress-text">
                    {{ Math.round(file.progress || 0) }}%
                  </span>
                </template>
              </div>
            </div>
            <div class="col-size">{{ formatFileSize(file.size) }}</div>
            <div class="col-action">
              <i class="fas fa-times remove-file" @click.stop="removeFile(index)"></i>
            </div>
          </div>
        </div>
      </div>
  
      <!-- Footer Buttons -->
      <div class="footer-btns">
        <button 
          class="next-btn" 
          :disabled="!canProceed"
          @click="handleNext"
        >
          <span>共 {{ files.length }} 个文件</span>
          <span class="divider">|</span>
          <span>下一步</span>
        </button>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, computed, watch } from 'vue'
  import { v4 as uuidv4 } from 'uuid'
  import { useRoute, useRouter } from 'vue-router'
  import { cookieUtils } from '@/utils/cookie'
  import { deleteFile } from '@/api/kb'
  
  interface FileItem {
    id: string
    file: File & { xhr?: XMLHttpRequest; id?: string }
    name: string
    size: number
    progress?: number
    error?: string
  }
  
  const props = defineProps<{
    files: FileItem[]
  }>()
  
  const emit = defineEmits<{
    (e: 'update:files', files: FileItem[]): void
    (e: 'next', files: FileItem[]): void
  }>()
  
  const route = useRoute()
  const router = useRouter()
  
  const isDragging = ref(false)
  const fileInput = ref<HTMLInputElement | null>(null)
  
  const ALLOWED_TYPES = [
    'text/plain',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/csv',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/pdf',
    'text/markdown',
    'text/html',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation'
  ]
  
  const MAX_FILE_SIZE = 100 * 1024 * 1024 // 100MB
  const MAX_FILES = 15
  
  const uploadingSet = ref<Set<string>>(new Set())
  
  const canProceed = computed(() => {
    return props.files.length > 0 && !props.files.some(f => f.error || (f.progress ?? 0) < 100)
  })
  
  watch(
    () => props.files.map(f => f.name + f.size),
    (newList, oldList) => {
      // 检查新加的文件，自动上传
      props.files.forEach((fileObj, idx) => {
        if ((fileObj.progress ?? 0) < 100 && !fileObj.error && !uploadingSet.value.has(fileObj.name + fileObj.size)) {
          uploadingSet.value.add(fileObj.name + fileObj.size)
          autoUpload(idx)
        }
      })
    },
    { immediate: true, deep: true }
  )
  
  function autoUpload(idx: number) {
    const updatedFiles = [...props.files]
    const fileObj = updatedFiles[idx]
    if (!fileObj || fileObj.error) return
    simulateFileUpload(fileObj.file, progress => {
      updatedFiles[idx].progress = progress
      emit('update:files', [...updatedFiles])
    }).then(() => {
      updatedFiles[idx].progress = 100
      emit('update:files', [...updatedFiles])
    }).catch(() => {
      updatedFiles[idx].error = '上传失败'
      emit('update:files', [...updatedFiles])
    })
  }
  
  function handleDragEnter(e: DragEvent) {
    isDragging.value = true
  }
  
  function handleDragLeave(e: DragEvent) {
    const rect = (e.currentTarget as HTMLElement).getBoundingClientRect()
    const x = e.clientX
    const y = e.clientY
    
    if (x <= rect.left || x >= rect.right || y <= rect.top || y >= rect.bottom) {
      isDragging.value = false
    }
  }
  
  function handleDrop(e: DragEvent) {
    isDragging.value = false
    if (e.dataTransfer?.files) {
      handleFiles(e.dataTransfer.files)
    }
  }
  
  function triggerFileInput() {
    fileInput.value?.click()
  }
  
  function handleFileSelect(e: Event) {
    const target = e.target as HTMLInputElement
    if (target.files) {
      handleFiles(target.files)
    }
    target.value = '' // Reset input
  }
  
  function validateFile(file: File): string | null {
    if (!ALLOWED_TYPES.includes(file.type)) {
      return '不支持的文件类型'
    }
    if (file.size > MAX_FILE_SIZE) {
      return '文件大小超过100MB限制'
    }
    return null
  }
  
  function handleFiles(newFiles: FileList | File[]) {
    if (props.files.length + newFiles.length > MAX_FILES) {
      alert(`一次最多只能上传${MAX_FILES}个文件`)
      return
    }
    const updatedFiles = [...props.files]
    Array.from(newFiles).forEach(file => {
      const error = validateFile(file)
      const fileId = uuidv4()
      // 将 id 保存到 file 对象中
      Object.defineProperty(file, 'id', { value: fileId })
      updatedFiles.push({
        id: fileId,
        file,
        name: file.name,
        size: file.size,
        error: error || undefined
      })
    })
    emit('update:files', updatedFiles)
  }
  
  async function removeFile(index: number) {
    const updatedFiles = [...props.files]
    const removed = updatedFiles[index]
    
    if (!removed) return
    
    if (removed?.file?.xhr) {
      removed.file.xhr.abort()
    }
    
    try {
      await deleteFile({ fileId: removed.id, kbId: route.query.kbId as string })
      // 删除成功后更新列表
      updatedFiles.splice(index, 1)
      uploadingSet.value.delete(removed.name + removed.size)
      emit('update:files', updatedFiles)
    } catch (error) {
      console.error('Failed to delete file:', error)
    }
  }
  
  function formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
  
  function handleNext() {
    // 只做跳转，不再负责上传
    emit('next', props.files)
  }
  
  function simulateFileUpload(file: File, onProgress: (progress: number) => void): Promise<void> {
    return new Promise((resolve, reject) => {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('fileId', (file as any).id);
      
      // 从路由query获取kbId
      const kbId = route.query.kbId as string;
      if (!kbId) {
        reject(new Error('缺少知识库ID'));
        return;
      }
      formData.append('kbId', kbId);
      
      // 从cookie获取creatorId
      const userCookie = cookieUtils.getCookie('user');
      let creatorId = '';
      if (userCookie) {
        try {
          const userObj = JSON.parse(userCookie);
          creatorId = userObj.username || '';
        } catch (e) {
          console.error('Failed to parse user cookie:', e);
        }
      }
      if (!creatorId) {
        reject(new Error('未获取到用户信息'));
        return;
      }
      formData.append('creatorId', creatorId);
      
      const xhr = new XMLHttpRequest();
      xhr.open('POST', '/api/upload');
      xhr.withCredentials = true; // 确保发送跨域请求时带上 cookies
      
      // 监听上传进度
      xhr.upload.onprogress = (event) => {
        if (event.lengthComputable) {
          const progress = (event.loaded / event.total) * 100;
          onProgress(progress);
        }
      };
      
      // 监听上传完成
      xhr.onload = () => {
        if (xhr.status === 200) {
          resolve();
        } else if (xhr.status === 401) {
          // 未登录或登录已过期，跳转到登录页
          router.push('/login');
          reject(new Error('未登录或登录已过期'));
        } else {
          reject(new Error(`上传失败: ${xhr.status}`));
        }
      };
      
      // 监听上传错误
      xhr.onerror = () => {
        reject(new Error('网络错误'));
      };
      
      // 监听上传取消
      xhr.onabort = () => {
        reject(new Error('上传已取消'));
      };
      
      // 发送请求
      xhr.send(formData);
      
      // 保存 xhr 实例到文件对象中，以便后续可以取消上传
      (file as any).xhr = xhr;
    });
  }
  </script>
  
  <style scoped>
  .file-upload {
    display: flex;
    flex-direction: column;
    gap: 24px;
    background: #1e1f23;
    flex: 1;
  }
  
  .upload-area {
    display: flex;
    justify-content: flex-start;
    padding: 0 48px;
    max-height: 130px;
    min-height: 130px;
    
  }
  
  .upload-box {
    border: 1.5px dashed #4fdbc9;
    border-radius: 12px;
    background: #25262b;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 15px 48px;
    cursor: pointer;
    transition: all 0.3s ease;
  }
  
  .upload-box.drag-over {
    border-color: #5b8cff;
    background: #2a2b2f;
  }
  
  .upload-icon {
    color: #5b8cff;
    font-size: 1.8rem;
  }
  
  .upload-tip {
    color: #f5f6fa;
    font-size: 1rem;
    font-weight: 500;
  }
  
  .upload-desc {
    color: #8b95a5;
    font-size: 0.8rem;
    text-align: center;
  }
  
  .file-list-container {
    padding: 0 48px;
    overflow-y: auto;
  }
  
  .file-list {
    width: 100%;
    display: flex;
    flex-direction: column;
    background: #25262b;
    border-radius: 8px;
    border: 1px solid #2a2b2f;
  }
  
  .file-list-header {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background: #2a2b2f;
    border-radius: 8px 8px 0 0;
    color: #8b95a5;
    font-size: 0.9rem;
  }
  
  .file-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #2a2b2f;
  }
  
  .file-item:last-child {
    border-bottom: none;
  }
  
  .col-name {
    flex: 2;
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 0;
  }
  
  .col-progress {
    flex: 2;
    position: relative;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .col-size {
    flex: 2;
    color: #8b95a5;
    text-align: center;
  }
  
  .col-action {
    flex: 1 0 60px;
    text-align: center;
  }
  
  .file-name {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #f5f6fa;
  }
  
  .file-icon {
    color: #5b8cff;
    font-size: 1.2rem;
    flex-shrink: 0;
  }
  
  .progress-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }
  
  .progress-bar {
    height: 2px;
    background: #2a2b2f;
    border-radius: 1px;
    overflow: hidden;
    width: 120px;
  }
  
  .progress {
    height: 100%;
    background: linear-gradient(90deg, #6a6ff7 0%, #6fd0ff 100%);
    border-radius: 1px;
    transition: width 0.3s ease;
  }
  
  .progress-text {
    margin-left: 10px;
    color: #6fd0ff;
    font-size: 0.9rem;
    white-space: nowrap;
  }
  
  .file-error {
    color: #ff4757;
    font-size: 0.9rem;
  }
  
  .remove-file {
    color: #8b95a5;
    cursor: pointer;
    padding: 8px;
    transition: all 0.3s ease;
  }
  
  .remove-file:hover {
    color: #ff4757;
  }
  
  .footer-btns {
    display: flex;
    justify-content: flex-end;
    padding: 0 48px;
    margin-bottom: 24px;
  }
  
  .next-btn {
    background: linear-gradient(90deg, #6a6ff7 0%, #6fd0ff 100%);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 10px 24px;
    font-size: 0.95rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .next-btn:hover {
    background: linear-gradient(90deg, #6e72e4 0%, #4cd6f8 100%);
  }
  
  .next-btn .divider {
    color: rgba(255, 255, 255, 0.5);
  }
  
  .next-btn:disabled {
    background: #2a2b2f;
    color: #8b95a5;
    cursor: not-allowed;
  }
  
  .next-btn:disabled .divider {
    color: rgba(139, 149, 165, 0.5);
  }
  </style>