import http from '@/utils/http'

export interface ModelParams {
  max_tokens: number;
  temperature: number;
}

export interface KnowledgeBase {
  kbId: string;
  kbName: string;
}

export interface Tool {
  toolId: string;
  toolName: string;
}

export interface FileUpload {
  fileId: string;
  fileName: string;
}

export interface AIModel {
  modelName: string;
  maxTokens: number;
  maxTemperature: number;
}

export interface KnowledgeSettingsConfig {
  searchMode: string;        //搜索模式：semantic/fulltext/hybrid  语义检索 全文检索 混合检索
  hybridWeight: number;    // 混合检索权重
  rerankEnabled: boolean;  // 是否启用重排序
  rerankWeight: number;    // 重排序权重
  rerankModel: string;      // 重排序模型
  quoteLimit: number;        // 引用上限
  minScore: number;            // 最低相关度分数
  optimizeEnabled: boolean;  // 是否启用问题优化
  optimizeModel: string;      // 问题优化使用的模型
  optimizeContext: string;
}


export interface FileSettingsConfig {
  maxFiles: number;
  isImage: boolean;
  isFile: boolean;
  isPdfEnhance: boolean;
  fileType: string[];
}


export interface AppDetailResponse {
  appName: string;
  creatorId: string;
  creatorName: string;
  appTypeId: string;
  appTypeName: string;
  appIcon: string;
  appBg: string;
  appDesc: string;
  chatModel: string;
  chatModelParams: ModelParams;
  prompt: string;
  selectedKB: KnowledgeBase[];
  knowledgeSettingsConfig: KnowledgeSettingsConfig;
  fileSettingsConfig: FileSettingsConfig;
  selectedTool: Tool[];
  selectedFile: FileUpload[];
  appPrivacy: string;
  appStatus: string;
}

export interface UpdateAppPayload {
  appId: string;
  appName: string;
  appTypeId: string;
  creatorId: string;
  appIcon: string;
  status: string;
  description: string;
  model: string;
  model_params: Record<string, any>;
  prompt: string;
  kb: KnowledgeBase;
  tool: Tool;
  fileUpload: FileUpload;
  privacy: string;
  updated_at: string;
}

export const modelApi = {
  getModels: async (): Promise<AIModel[]> => {
    const res = await http.get<AIModel[]>('/api/models');
    return res.data;
  },
  getAppDetail: async (appId: string): Promise<AppDetailResponse> => {
    const res = await http.get<AppDetailResponse>('/api/app-detail', { params: {appId: appId } });
    return res.data;
  },
  updateApp: async (data: any) => {
    const res = await http.post('/api/update-app', data);
    return res.data;
  }
}