<template>
  <div class="file-settings">
    <!-- 文件解析设置 -->
    <div class="settings-section">
      <h3 class="section-title">文件解析设置</h3>
      <label class="checkbox-wrap">
        <input type="checkbox" v-model="formData.pdfEnhanced">
        <span class="label">PDF增强解析</span>
        <i class="fas fa-question-circle help-icon" title="增强PDF文件的解析效果"></i>
      </label>
    </div>

    <!-- 数据处理方式设置 -->
    <div v-if="sections[1]" class="settings-section">
      <h3 class="section-title">{{ sections[1].title }}</h3>
      <!-- 处理方式选择 -->
      <div v-for="item in sections[1].items" :key="item.id" class="setting-item">
        <div class="setting-label">
          <span>{{ item.label }}</span>
          <i v-if="item.tooltip" class="fas fa-question-circle help-icon" :title="item.tooltip"></i>
        </div>
        <div class="setting-control">
          <div class="radio-group">
            <label v-for="option in item.options" :key="option.value" class="radio-item1" :class="{ selected: formData[item.id] === option.value }">
              <input type="radio" v-model="formData[item.id]" :value="option.value">
              <span class="radio-label">{{ option.label }}</span>
              <i v-if="option.tooltip" class="fas fa-question-circle help-icon" :title="option.tooltip"></i>
            </label>
          </div>
        </div>
      </div>

      <!-- 分块存储设置 -->
      <template v-if="formData.processMethod === 'split' && sections[1].splitStorageSettings">
        <!-- 分块条件 -->
        <div class="setting-item">
          <div class="setting-label">
            <span>分块条件</span>
            <i class="fas fa-question-circle help-icon" title="设置文本分块的条件"></i>
          </div>
          <div class="setting-control">
            <div class="select-group">
              <select v-model="formData.splitCondition">
                <option value="greater">原文长度大于</option>
                <option value="model_context">原文长度小于文件处理模型最大上下文70%</option>
                <option value="force">强制分块</option>
              </select>
              <input 
                v-if="formData.splitCondition === 'greater'"
                type="number" 
                v-model="formData.splitValue" 
                class="number-input"
              >
            </div>
          </div>
        </div>

        <!-- 索引增强 -->
        <div class="setting-item">
          <div class="setting-label">
            <span>索引增强</span>
            <i class="fas fa-question-circle help-icon" title="增强索引效果"></i>
          </div>
          <div class="setting-control">
            <div class="checkbox-group">
              <div class="checkbox-row">
                <label class="checkbox-item">
                  <input type="checkbox" v-model="formData.autoIndex">
                  <span class="checkbox-label">自动生成补充索引</span>
                  <i class="fas fa-question-circle help-icon" title="自动为文档生成补充索引"></i>
                </label>
                <label class="checkbox-item">
                  <input type="checkbox" v-model="formData.imageIndex">
                  <span class="checkbox-label">图片自动索引</span>
                  <i class="fas fa-question-circle help-icon" title="自动为图片生成索引"></i>
                </label>
              </div>
            </div>
          </div>
        </div>

        <!-- 自定义设置 -->
        <div class="setting-item">
          <div class="setting-label">
            <span>分块处理参数</span>
            <i class="fas fa-question-circle help-icon" title="设置分块处理的参数"></i>
          </div>
          <div class="setting-control">
            <div class="radio-group vertical">
              <label class="radio-item-default" :class="{ selected: formData.splitParams === 'default' }">
                <input type="radio" v-model="formData.splitParams" value="default">
                <span class="radio-label">默认</span>
              </label>
              
              <div class="custom-container" :class="{ selected: formData.splitParams === 'custom' }">
                <label class="radio-item2">
                  <input type="radio" v-model="formData.splitParams" value="custom">
                  <span class="radio-label">自定义</span>
                </label>

                <!-- 自定义设置内容 -->
                <div v-if="formData.splitParams === 'custom'" class="custom-settings">
                  <div class="content-item">
                    <div class="radio-group">
                      <label v-for="option in splitTypes" :key="option.value" class="radio-item" :class="{ selected: formData.splitType === option.value }">
                        <input type="radio" v-model="formData.splitType" :value="option.value">
                        <span class="radio-label">{{ option.label }}</span>
                        <i v-if="option.tooltip" class="fas fa-question-circle help-icon" :title="option.tooltip"></i>
                      </label>
                    </div>
                  </div>

                  <div class="content-item" v-if="formData.splitType !== 'length' && formData.splitType !== 'ratio'">
                    <label>最大段落深度</label>
                    <input type="number" v-model="formData.maxParagraphDepth" class="number-input">
                  </div>

                  <div class="content-item" v-if="formData.splitType !== 'ratio'">
                    <label>最大分块大小</label>
                    <input type="number" v-model="formData.maxBlockSize" class="number-input">
                  </div>
                  <div class="content-item" v-if="formData.splitType === 'ratio'">
                    <label>分割符</label>
                    <select v-model="formData.splitRatio" class="select-input">
                      <option v-for="option in splitRatioOptions" :key="option.value" :value="option.value">{{ option.label }}</option>
                    </select>
                    <input v-if="formData.splitRatio === 'custom'" v-model="formData.splitRatioCustom" class="number-input" placeholder="请输入自定义分割符" />
                  </div>
                  <div class="content-item">
                    <label>索引大小</label>
                    <select v-model="formData.indexSize" class="select-input">
                      <option v-for="option in indexSizeOptions" :key="option.value" :value="option.value">{{ option.label }}</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>

      <!-- 问答对提取设置 -->
      <template v-if="formData.processMethod === 'qa'">
        <div class="setting-item">
          <div class="setting-label">
            <span>问答对提取参数</span>
            <i class="fas fa-question-circle help-icon" title="设置问答对提取的参数"></i>
          </div>
          <div class="setting-control">
            <div class="radio-group vertical">
              <label class="radio-item-default" :class="{ selected: formData.qaParams === 'default' }">
                <input type="radio" v-model="formData.qaParams" value="default">
                <span class="radio-label">默认</span>
              </label>
              <div class="custom-container" :class="{ selected: formData.qaParams === 'custom' }">
                <label class="radio-item2">
                  <input type="radio" v-model="formData.qaParams" value="custom">
                  <span class="radio-label">自定义</span>
                </label>
                <div v-if="formData.qaParams === 'custom'" class="custom-settings">
                  <div class="content-item">
                    <div class="radio-group">
                      <label v-for="option in splitTypes" :key="option.value" class="radio-item" :class="{ selected: formData.splitType === option.value }">
                        <input type="radio" v-model="formData.splitType" :value="option.value">
                        <span class="radio-label">{{ option.label }}</span>
                        <i v-if="option.tooltip" class="fas fa-question-circle help-icon" :title="option.tooltip"></i>
                      </label>
                    </div>
                  </div>
                  <div class="content-item" v-if="formData.splitType !== 'length' && formData.splitType !== 'ratio'">
                    <label>最大段落深度</label>
                    <input type="number" v-model="formData.maxParagraphDepth" class="number-input">
                  </div>
                  <div class="content-item" v-if="formData.splitType !== 'ratio'">
                    <label>最大分块大小</label>
                    <input type="number" v-model="formData.maxBlockSize" class="number-input">
                  </div>
                  <div class="content-item" v-if="formData.splitType === 'ratio'">
                    <label>分割符</label>
                    <select v-model="formData.splitRatio" class="select-input">
                      <option v-for="option in splitRatioOptions" :key="option.value" :value="option.value">{{ option.label }}</option>
                    </select>
                    <input v-if="formData.splitRatio === 'custom'" v-model="formData.splitRatioCustom" class="number-input" placeholder="请输入自定义分割符" />
                  </div>
                  <div class="content-item qa-prompt-wrap" @mouseenter="showEditBtn = true" @mouseleave="showEditBtn = false" style="position:relative;">
                    <label>QA 拆分引导词</label>
                    <div class="qa-prompt-readonly">
                      <pre>{{ formData.qaPrompt }}</pre>
                      <div v-if="showEditBtn" class="qa-prompt-gradient-blur"></div>
                    </div>
                    <button
                      v-if="showEditBtn"
                      class="edit-prompt-btn"
                      @click="openPromptDialog"
                      style="position:absolute; right:10px; bottom:10px; display:flex; align-items:center; z-index:2;"
                    >
                      <i class="fas fa-pen"></i> 自定义提示词
                    </button>
                    <CustomPromptDialog
                      v-if="showPromptDialog"
                      :model-value="formData.qaPrompt"
                      :fixed-content="qaPromptFixedContent"
                      @update:model-value="onPromptUpdate"
                      @close="showPromptDialog = false"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>

    <div class="footer-btns">
      <button class="next-btn" @click="handleNext">下一步</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import CustomPromptDialog from '@/components/knowledge/CustomPromptDialog.vue'

// 文件上传项
// 表单数据结构
interface FormData {
  pdfEnhanced: boolean; // 是否开启PDF增强解析
  processMethod: 'split' | 'qa'; // 处理方式：分块存储/问答对提取
  splitCondition: 'greater' | 'less' | 'equal'; // 分块条件
  splitValue: number; // 分块条件的数值
  autoIndex: boolean; // 是否自动生成补充索引
  imageIndex: boolean; // 是否图片自动索引
  splitParams: 'default' | 'custom'; // 分块处理参数：默认/自定义
  splitType: 'paragraph' | 'length' | 'ratio'; // 分块方式
  maxParagraphDepth: number; // 最大段落深度
  maxBlockSize: number; // 最大分块大小
  indexSize: string; // 索引大小
  qaPrompt: string; // QA 拆分引导词
  splitRatio: string; // 分割符类型
  splitRatioCustom: string; // 自定义分割符内容
  [key: string]: any; // 其他动态字段
}

// 设置项选项
interface SettingOption {
  value?: string; // 选项值
  label: string; // 选项显示文本
  tooltip?: string; // 选项提示
  desc?: string; // 选项描述
  id?: string; // 选项ID
}

// 复选框选项
interface CheckboxOption extends SettingOption {
  id: string; // 复选框ID
}

// 设置项结构
interface SettingItem {
  id: string; // 字段ID
  label: string; // 字段显示名
  type: 'switch' | 'radio-group' | 'radio-group-vertical' | 'select-number' | 'checkbox-group' | 'number-input' | 'select' | 'textarea'; // 控件类型
  tooltip?: string; // 字段提示
  desc?: string; // 字段描述
  options?: SettingOption[] | CheckboxOption[]; // 可选项
  placeholder?: string; // 占位符
  defaultValue?: number | string; // 默认值
}

// 设置分区结构
interface SettingSection {
  id: string; // 分区ID
  title: string; // 分区标题
  items: SettingItem[]; // 分区下的设置项
  splitStorageSettings?: SettingItem[]; // 分块存储设置项
  splitCustomSettings?: SettingItem[]; // 分块自定义设置项
  qaSettings?: SettingItem[]; // 问答对设置项
  qaCustomSettings?: SettingItem[]; // 问答对自定义设置项
}

// 接收文件列表属性
const props = defineProps<{
  settings?: FormData
}>()

const emit = defineEmits<{
  (e: 'next', settings: FormData): void
  (e: 'update:settings', settings: FormData): void
}>()

// 配置数据
const sections: SettingSection[] = [
  // 第一部分：文件解析设置
  {
    id: 'fileParseSettings',
    title: '文件解析设置',
    items: [
      {
        id: 'pdfEnhanced',
        label: 'PDF增强解析',
        type: 'switch',
        tooltip: '增强PDF文件的解析效果'
      }
    ]
  },
  // 第二部分：数据处理方式设置
  {
    id: 'dataProcessSettings',
    title: '数据处理方式设置',
    items: [
      {
        id: 'processMethod',
        label: '处理方式',
        type: 'radio-group',
        options: [
          {
            value: 'split',
            label: '分块存储',
            tooltip: '将文档按照设定规则分块存储'
          },
          {
            value: 'qa',
            label: '问答对提取',
            tooltip: '从文档中提取问答对'
          }
        ]
      }
    ],
    // 分块存储的设置
    splitStorageSettings: [
      {
        id: 'splitCondition',
        label: '分块条件',
        type: 'select-number',
        tooltip: '设置文本分块的条件',
        options: [
          { value: 'greater', label: '原文长度大于' },
          { value: 'model_context', label: '原文长度小于文件处理模型最大上下文70%' },
          { value: 'force', label: '强制分块' }
        ]
      },
      {
        id: 'indexEnhancement',
        label: '索引增强',
        type: 'checkbox-group',
        options: [
          {
            value: 'autoIndex',
            label: '自动生成补充索引',
            tooltip: '自动为文档生成补充索引'
          },
          {
            value: 'imageIndex',
            label: '图片自动索引',
            tooltip: '自动为图片生成索引'
          }
        ]
      },
      {
        id: 'splitParams',
        label: '分块处理参数',
        type: 'radio-group-vertical',
        options: [
          {
            value: 'default',
            label: '默认',
            desc: '使用系统默认的参数和规则'
          },
          {
            value: 'custom',
            label: '自定义',
            desc: '自定义设置数据处理规则'
          }
        ]
      }
    ],
    // 分块存储的自定义设置
    splitCustomSettings: [
      {
        id: 'splitType',
        label: '分块方式',
        type: 'radio-group',
        options: [
          {
            value: 'paragraph',
            label: '按段落分块',
            tooltip: '根据文档的段落结构进行分块'
          },
          {
            value: 'length',
            label: '按长度分块',
            tooltip: '根据文本长度进行分块'
          },
          {
            value: 'ratio',
            label: '按指定分割符分块',
            tooltip: '根据指定的分隔符进行分块'
          }
        ]
      },
      {
        id: 'maxParagraphDepth',
        label: '最大段落深度',
        type: 'number-input',
        defaultValue: 5
      },
      {
        id: 'maxBlockSize',
        label: '最大分块大小',
        type: 'number-input',
        defaultValue: 1000
      },
      {
        id: 'indexSize',
        label: '索引大小',
        type: 'select',
        options: [
          { value: '64', label: '64' },
          { value: '128', label: '128' },
          { value: '256', label: '256' },
          { value: '512', label: '512' },
          { value: '768', label: '768' },
          { value: '1024', label: '1024' },
          { value: '1536', label: '1536' },
          { value: '2048', label: '2048' },
          { value: '3072', label: '3072' }
        ]
      }
    ],
    // 问答对提取的设置
    qaSettings: [
      {
        id: 'qaParams',
        label: '问答对提取参数',
        type: 'radio-group-vertical',
        options: [
          {
            value: 'default',
            label: '默认',
            desc: '使用系统默认的参数和规则'
          },
          {
            value: 'custom',
            label: '自定义',
            desc: '自定义设置数据处理规则'
          }
        ]
      }
    ],
    // 问答对提取的自定义设置
    qaCustomSettings: [
      {
        id: 'splitType',
        label: '分块方式',
        type: 'radio-group',
        options: [
          {
            value: 'paragraph',
            label: '按段落分块',
            tooltip: '根据文档的段落结构进行分块'
          },
          {
            value: 'length',
            label: '按长度分块',
            tooltip: '根据文本长度进行分块'
          },
          {
            value: 'ratio',
            label: '按指定分割符分块',
            tooltip: '根据指定的分隔符进行分块'
          }
        ]
      },
      {
        id: 'maxParagraphDepth',
        label: '最大段落深度',
        type: 'number-input',
        defaultValue: 5
      },
      {
        id: 'maxBlockSize',
        label: '最大分块大小',
        type: 'number-input',
        defaultValue: 1000
      },
      {
        id: 'qaPrompt',
        label: 'QA 拆分引导词',
        type: 'textarea',
        defaultValue: `<Context></Context> 标记中是一段文本，学习和分析它，并整理学习成果：
- 提出问题并给出每个问题的答案。
- 答案需详细完整，尽可能保留原文描述，可以适当扩展答案描述。
- 答案可以包含普通文字、链接、代码、表格、公示、媒体链接等 Markdown 元素。
- 最多提出 50 个问题。
- 生成的问题和答案和源文本语言相同。`
      }
    ]
  }
]

const splitTypes = sections[1].splitCustomSettings?.find(item => item.id === 'splitType')?.options || []

const indexSizeOptions = sections[1].splitCustomSettings?.find(item => item.id === 'indexSize')?.options || []

// 分割符选项
const splitRatioOptions = [
  { value: '', label: '不设置' },
  { value: '\n', label: '1 个换行符' },
  { value: '\n\n', label: '2 个换行符' },
  { value: '。', label: '句号' },
  { value: '！', label: '感叹号' },
  { value: '？', label: '问号' },
  { value: '，', label: '分号' },
  { value: '=====', label: '=====' },
  { value: 'custom', label: '自定义' }
]

// 自动生成默认表单数据
function getDefaultFormData(): FormData {
  const data: any = {
    pdfEnhanced: false,
    splitValue: 1000,
    autoIndex: false,
    imageIndex: false,
    maxParagraphDepth: 5,
    maxBlockSize: 1000,
    indexSize: '64',
    qaPrompt: `<Context></Context> 标记中是一段文本，学习和分析它，并整理学习成果：\n- 提出问题并给出每个问题的答案。\n- 答案需详细完整，尽可能保留原文描述，可以适当扩展答案描述。\n- 答案可以包含普通文字、链接、代码、表格、公示、媒体链接等 Markdown 元素。\n- 最多提出 50 个问题。\n- 生成的问题和答案和源文本语言相同。`,
    splitRatio: '',
    splitRatioCustom: ''
  };
  // 遍历sections，自动取options第一个为默认值
  sections.forEach(section => {
    if (section.items) {
      section.items.forEach(item => {
        if (item.options && item.options.length > 0) {
          data[item.id] = item.options[0].value ?? item.options[0].id;
        }
      });
    }
    ['splitStorageSettings', 'splitCustomSettings', 'qaSettings', 'qaCustomSettings'].forEach(key => {
      if ((section as any)[key]) {
        (section as any)[key].forEach((item: any) => {
          if (item.options && item.options.length > 0) {
            data[item.id] = item.options[0].value ?? item.options[0].id;
          } else if (item.defaultValue !== undefined) {
            data[item.id] = item.defaultValue;
          }
        });
      }
    });
  });
  return data;
}

const defaultFormData: FormData = getDefaultFormData();
const formData = ref<FormData>(props.settings ? { ...props.settings } : { ...defaultFormData });

// 父组件传入settings时覆盖本地formData
watch(() => props.settings, (val) => {
  if (val) {
    formData.value = { ...val }
  }
}, { immediate: true })

// formData变化时同步到父组件
watch(formData, (val) => {
  emit('update:settings', { ...val })
}, { deep: true })

// 处理下一步
function handleNext() {
  emit('next', formData.value)
}

const showEditBtn = ref(false)
const showPromptDialog = ref(false)
function onPromptUpdate(val: string) {
  formData.value.qaPrompt = val
}
function openPromptDialog() {
  showPromptDialog.value = true
}

const qaPromptFixedContent = `
请按以下格式整理学习成果：
<Context>
文本
</Context>
Q1: 问题。
A1: 答案。
Q2:
A2:

------

我们开始吧！

<Context>
{{text}}
</Context>
`
</script>

<style scoped>
.file-settings {
  padding: 0 12px;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  max-width: 640px;
  width: 640px;
  margin: 0 auto;
}

.settings-section {
  padding: 24px 0 5px 0;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.section-title {
  color: #f5f6fa;
  font-size: 16px;
  font-weight: 500;
  margin: 0;
}

.setting-item {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 20px;
}

.setting-label {
  color: #8b95a5;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.setting-control {
  flex: 1;
  min-width: 0; /* 防止子元素溢出 */
}

.help-icon {
  color: #8b95a5;
  font-size: 14px;
  cursor: help;
}

/* Switch 样式 */
.switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #2a2b2f;
  transition: .4s;
  border-radius: 20px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: #8b95a5;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #5b8cff;
}

input:checked + .slider:before {
  transform: translateX(20px);
  background-color: white;
}

.setting-desc {
  color: #8b95a5;
  font-size: 12px;
  margin-left: 8px;
}

/* Radio 样式 */
.radio-group {
  display: flex;
  gap: 24px;
}

.radio-group.vertical {
  flex-direction: column;
  gap: 16px;
}

.radio-item3 {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  border-radius: 6px;
  padding: 12px 30px;
  height: 60px;
}

.radio-item1 {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  border: 1px solid #8b95a5;
  border-radius: 6px;
  padding: 12px 30px;
  height: 45px;
  width: 270px;
  
}

.radio-item2 {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  border-radius: 6px;
  padding: 12px 10px;
  height: 45px;
  width: 100%;
  box-sizing: border-box;
}

.radio-label {
  color: #f5f6fa;
  font-size: 14px;
}

.radio-desc {
  color: #8b95a5;
  font-size: 12px;
  margin-left: 4px;
}

/* Select 和 Input 样式 */
.select-group {
  display: flex;
  gap: 25px;
  align-items: center;
}

select, .number-input {
  background: #1e1f23;
  border: 1px solid #8b95a5;
  color: #f5f6fa;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  outline: none;
  height: 40px;
  width: 270px;
  margin-right: 10px;
}

select:focus, .number-input:focus {
  border-color: #59dbdb;
}

.number-input {
  width: 270px;
  -moz-appearance: textfield;
}

.number-input::-webkit-inner-spin-button,
.number-input::-webkit-outer-spin-button {
  opacity: 1;
  height: 20px;
  cursor: pointer;
}

/* Checkbox 样式 */
.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.checkbox-row {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  border-radius: 6px;
  height: 45px;
  width: 270px;
}

.checkbox-label {
  color: #f5f6fa;
  font-size: 14px;
}


.checkbox-item input[type="checkbox"] {
  margin: 0;
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.checkbox-item .checkbox-label {
  display: flex;
  align-items: center;
  height: 100%;
}

/* Footer 样式 */
.footer-btns {
  display: flex;
  justify-content: flex-end;
  padding: 14px 0;
}

.next-btn {
  background: linear-gradient(90deg, #6a6ff7 0%, #6fd0ff 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 24px;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.next-btn:hover {
  background: linear-gradient(90deg, #6e72e4 0%, #4cd6f8 100%);
}

.number-input-wrap {
  display: flex;
  align-items: center;
}

.number-input-wrap .number-input {
  width: 120px;
  background: #2a2b2f;
  border: 1px solid #2a2b2f;
  color: #f5f6fa;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  outline: none;
}

.number-input-wrap .number-input:focus {
  border-color: #5b8cff;
}

.select-wrap select {
  width: 200px;
  background: #2a2b2f;
  border: 1px solid #2a2b2f;
  color: #f5f6fa;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  outline: none;
}

.select-wrap select:focus {
  border-color: #5b8cff;
}

.textarea-wrap {
  width: 100%;
}

.textarea-input {
  width: 100%;
  min-height: 150px;
  background: #2a2b2f;
  border: 1px solid #2a2b2f;
  color: #f5f6fa;
  padding: 12px;
  border-radius: 6px;
  font-size: 14px;
  line-height: 1.5;
  font-family: inherit;
  resize: vertical;
  outline: none;
}

.textarea-input:focus {
  border-color: #5b8cff;
}

.checkbox-wrap {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  color: #f5f6fa;
  font-size: 14px;
  border: 1px solid #59dbdb;
  border-radius: 6px;
  padding: 12px 30px;
  height: 60px;
}

.checkbox-wrap input[type="checkbox"] {
  width: 16px;
  height: 16px;
  margin: 0;
  cursor: pointer;
}

.checkbox-wrap .help-icon {
  color: #8b95a5;
  font-size: 14px;
  cursor: help;
}

.custom-container {
  border: 1px solid #8b95a5;
  border-radius: 8px;
  min-height: 55px;
}

.custom-container.selected {
  border-color: #59dbdb;
  box-shadow: 0 0 0 2px rgba(91,140,255,0.15);
}

.custom-container .radio-item2 {
  border: none;
  width: 100%;
  height: auto;
}

.custom-settings {
  margin-top: 0;
  padding: 0;
  border: none;
}

.content-item {
  margin-bottom: 10px;
  margin-left: 25px;
}

.content-item:last-child {
  margin-bottom: 10px;
  margin-right: 25px;
}

.content-item label {
  display: block;
  color: #8b95a5;
  font-size: 14px;
  margin-bottom: 12px;
}

.radio-item-default {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  border: 1px solid #8b95a5;
  border-radius: 6px;
  padding: 12px 10px;
  height: 55px;
  width: 100%;
  box-sizing: border-box;
  background: none;
}

.radio-item1.selected {
  border-color: #59dbdb;
  box-shadow: 0 0 0 2px rgba(91,140,255,0.15);
}

.radio-item-default.selected,
.radio-item2.selected,
.radio-item1.selected {
  border-color: #59dbdb;
  box-shadow: 0 0 0 2px rgba(91,140,255,0.15);
}

.radio-item {
  display: flex;
  align-items: center;
  cursor: pointer;
  border-radius: 6px;
  padding: 12px 10px;
  height: 45px;
}

.radio-item input[type="radio"] {
  margin-right: 6px; /* 你可以根据需要调整为16px或其他数值 */
}

.qa-prompt-readonly {
  position: relative;
  width: 100%;
  min-height: 150px;
  background: #2a2b2f;
  border: 1px solid #2a2b2f;
  color: #f5f6fa;
  padding: 12px;
  border-radius: 6px;
  font-size: 14px;
  line-height: 1.5;
  font-family: inherit;
  white-space: pre-wrap;
  word-break: break-all;
  box-sizing: border-box;
  resize: none;
  outline: none;
  overflow: hidden;
  transition: filter 0.3s;
}

.qa-prompt-readonly.blurred {
  filter: blur(4px) brightness(1.1);
}

.edit-prompt-btn {
  border: 1.5px solid #59dbdb;
  background: #23242a;
  color: #59dbdb;
  border-radius: 6px;
  padding: 6px 16px;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 6px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  z-index: 2;
  transition: border-color 0.2s, color 0.2s;
  cursor: pointer;
}

.edit-prompt-btn:hover {
  border-color: #6fd0ff;
  color: #6fd0ff;
}

.qa-prompt-gradient-blur {
  pointer-events: none;
  position: absolute;
  left: 0; right: 0; top: 0; bottom: 0;
  background: linear-gradient(to bottom, rgba(42,43,47,0) 60%, rgba(42,43,47,0.7) 100%);
  -webkit-backdrop-filter: blur(1px);
  backdrop-filter: blur(1px);
  z-index: 1;
}
</style> 