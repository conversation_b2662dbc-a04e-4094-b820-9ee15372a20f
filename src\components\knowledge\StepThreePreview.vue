<template>
  <div class="step-preview">
    <div class="main">
        <div class="header-row">
        <div class="header-left">文件列表</div>
        <div class="header-right">
            分块预览
            <span class="stat">共 0 个分块，最多展示 10 个</span>
        </div>
        </div>
        <div class="main-row">
        <div class="file-list">
            <div v-for="(file, idx) in props.files" :key="file.name" :class="['file-item', idx === 0 ? 'selected' : '']">
              <span class="file-name">{{ file.name }}</span>
            </div>
        </div>
        <div class="preview-area">
            <div class="preview-empty">
            <i class="preview-icon">📦</i>
            <div class="preview-tip">点击左侧文件后进行预览</div>
            </div>
        </div>
        </div>
    </div>
    <div class="footer">
        <div class="footer-btns">
        <button class="next-btn" @click="emit('next')">下一步</button>
        </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const emit = defineEmits(['next'])
const props = defineProps<{ files: { name: string }[] }>()
</script>

<style scoped>
.step-preview {
  display: flex;
  flex-direction: column;
  height: 100%;
  border-radius: 16px;
  box-sizing: border-box;
  height: 100%;
  margin: 12px;
}
.main {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  margin-left: 20px;
  margin-right: 20px;
  border-radius: 16px;
  background: #18191c;
  overflow: hidden;
}
.footer {
  margin-top: 20px;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}
.header-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  border-bottom: 1px solid #23242a;
  font-size: 17px;
  font-weight: 500;
  color: #f5f6fa;
}
.header-left {
  flex: 1;
  border-right: 1px solid #23242a;
  padding-top: 12px;
  padding-bottom: 12px;
  padding: 12px 0 12px 20px;
  background: #18191c;

}
.header-right {
  flex: 2;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 16px;
  padding-left: 20px;
  padding-top: 12px;
  padding-bottom: 12px;
  background: #18191c;
  
}
.stat {
  font-size: 13px;
  color: #8b95a5;
  font-weight: 400;
  margin-left: 12px;
}
.main-row {
  display: flex;
  flex: 1;
  min-height: 0;
}
.file-list {
  flex: 1;
  background: #18191c;
  border-right: 1px solid #23242a;
  padding: 20px 0 0 20px;
  display: flex;
  flex-direction: column;
  gap: 18px;
  overflow-y: auto;
}
.file-item {
  display: flex;
  align-items: center;
  background: #23242a;
  border-radius: 6px;
  font-size: 14px;
  color: #f5f6fa;
  cursor: pointer;
  transition: background 0.2s, box-shadow 0.2s;
  box-shadow: none;
  margin-right: 20px;
  padding: 6px 12px;
}
.file-item.selected,
.file-item:hover {
  background: #232f3b;
  box-shadow: 0 2px 8px rgba(91,189,189,0.10);
}
.file-icon {
  width: 32px;
  height: 32px;
  background: #22343c;
  color: #54bdbd;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 15px;
  font-weight: bold;
  margin-right: 16px;
}
.file-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.preview-area {
  flex: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #18191c;
  position: relative;
  margin-left: 20px;
  overflow-y: auto;
}
.preview-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #8b95a5;
  width: 100%;
  height: 100%;
}
.preview-icon {
  font-size: 38px;
  margin-bottom: 18px;
  color: #232f3b;
}
.preview-tip {
  font-size: 16px;
  color: #8b95a5;
}
.footer-btns {
  display: flex;
  justify-content: flex-end;
  padding: 0 32px 0 0;
 
}
.next-btn {
  background: linear-gradient(90deg, #6a6ff7 0%, #6fd0ff 100%);
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 10px 32px;
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.2s;
}
.next-btn:hover {
  background: linear-gradient(90deg, #6e72e4 0%, #4cd6f8 100%);
}
</style> 