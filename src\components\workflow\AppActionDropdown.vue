<template>
  <div class="app-action-dropdown">
    <!-- 第一部分：去对话 -->
    <div class="dropdown-item" @click="handleAction('chat')">
      <i class="fas fa-comments"></i>
      <span>去对话</span>
    </div>
    <div class="dropdown-divider"></div>

    <!-- 第二部分：编辑信息、权限 -->
    <div class="dropdown-item" @click="handleAction('edit')">
      <i class="fas fa-edit"></i>
      <span>编辑信息</span>
    </div>
    <div class="dropdown-item" @click="handleAction('permission')">
      <i class="fas fa-key"></i>
      <span>权限</span>
    </div>
    <div class="dropdown-divider"></div>

    <!-- 第三部分：创建副本、删除 -->
    <div class="dropdown-item" @click="handleAction('copy')">
      <i class="fas fa-copy"></i>
      <span>创建副本</span>
    </div>
    <div class="dropdown-item danger" @click="handleAction('delete')">
      <i class="fas fa-trash"></i>
      <span>删除</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineEmits } from 'vue';

interface ActionPayload {
  action: string;
  appId: string;
}

const emit = defineEmits<{
  action: [payload: ActionPayload];
}>();

const props = defineProps<{
  appId: string;
}>();

function handleAction(action: string) {
  emit('action', { action, appId: props.appId });
}
</script>

<style scoped>
.app-action-dropdown {
  width: 160px;
  min-width: 160px;
  background: #23242a;
  border-radius: 1rem;
  box-shadow: 0 4px 32px #000a, 0 1.5px 8px #4fc3ff11 inset;
  padding: 0.4rem 0;
  display: flex;
  flex-direction: column;
  z-index: 1000;
  border: 1px solid rgba(79, 195, 255, 0.1);
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  padding: 0.6rem 1rem;
  cursor: pointer;
  transition: background 0.18s, color 0.18s;
  color: #cfd8dc;
  font-size: 0.85rem;
}

.dropdown-item:hover {
  background: linear-gradient(90deg, #2d3651 0%, #3a4a6b 100%);
  color: #fff;
}

.dropdown-item.danger {
  color: #ff6b6b;
}

.dropdown-item.danger:hover {
  background: linear-gradient(90deg, #4a2c2c 0%, #5c3535 100%);
  color: #ff8a8a;
}

.dropdown-item i {
  font-size: 0.9rem;
  width: 16px;
  text-align: center;
  flex-shrink: 0;
}

.dropdown-divider {
  height: 1px;
  background: rgba(79, 195, 255, 0.1);
  margin: 0.3rem 0.8rem;
}
</style>
