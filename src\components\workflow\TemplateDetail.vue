<template>
  <div class="template-detail-bg">
    <div class="main-card">
      <div class="header">
        <button class="back-btn" @click="goToApps">
          <i class="fas fa-chevron-left"></i>
          全部应用
        </button>
        <button class="save-btn" @click="saveApp">保存</button>
      </div>
      <div class="main-content-wrap">
        <!-- 左侧卡片区 -->
        <div class="left-panel">
          <div class="app-card">
            <div class="app-title">
              <div class="card-icon" :style="{background: appBg}">
                <i :class="appIcon" class="app-icon"></i>
              </div>
              <span>{{ appName }}</span>
            </div>
            <div class="app-desc">{{ appDesc }}</div>
            <div class="app-actions">
              <button class="action-btn" @click="goToChat">对话</button>
              <button class="action-btn">设置</button>
            </div>
          </div>
          <div class="section-card">
            <div class="ai-config">
              <div class="section-title">AI 配置</div>
              <div class="config-row horizontal">
                <span>AI模型</span>
                <select v-model="chatModel" @change="onModelChange(chatModel)">
                  <option v-for="m in chatModels" :key="m.modelName" :value="m.modelName">
                    {{ m.modelName }}
                  </option>
                </select>
              </div>
              <div class="config-row">
                <span>提示词</span>
                <textarea class="prompt-input" :placeholder="promptPlaceholder">{{ prompt }}</textarea>
              </div>
            </div>
            <div
                class="section"
                v-for="section in sections"
                :key="section.key"
            >
                <div
                  class="section-row"
                  :class="{ 'no-border': section.key === 'knowledge' }"
                >
                  <div class="section-title">{{ section.title }}</div>
                  <div class="section-btn-group">
                    <button
                      v-for="btn in section.btnText"
                      :key="btn"
                      class="section-btn"
                      @click="handleSectionBtnClick(btn, section)"
                    >
                      {{ btn }}
                    </button>
                  </div>
                </div>
                <KnowledgeConfigSummary
                  v-if="section.key === 'knowledge'"
                  :config="knowledgeConfig"
                  :selectedKB="selectedKB"
                />
            </div>
          </div>
        </div>
        <!-- 右侧调试区 -->
        <div class="right-panel">
          <div class="preview-title">调试预览</div>
          <div class="preview-area">
            <ChatMain
             :appId="appId"
             :activeChatId="activeChatId"
             :activeChatTitle="activeChatTitle"
            />
          </div>
        </div>
      </div>
    </div>
    <SelectKnowledgeBaseDialog
      :visible="showKbDialog"
      :selectedKB="selectedKB.map(kb => ({ kbId: kb.kbId || '', kbName: kb.kbName || '' }))"
      @close="showKbDialog = false"
      @select="handleKbSelect"
    />
    <SelectKnowledgeConfigDialog
      :visible="showConfigDialog"
      :chatModels="chatModels"
      :defaultConfig="knowledgeConfig"
      @close="showConfigDialog = false"
      @select="handleConfigSelect"
    />
  </div>
</template>

<script setup lang="ts">
import { useRouter, useRoute} from 'vue-router'
import { ref, onMounted, watch } from 'vue'
import { modelApi, AIModel, KnowledgeBase, ModelParams, Tool, FileUpload } from '@/api/model'
import { cookieUtils } from '@/utils/cookie'
import dayjs from 'dayjs'
import ChatMain from '@/components/chat/ChatMain.vue'
import { useAppStore } from '@/store/app'
import SelectKnowledgeBaseDialog from './SelectKnowledgeBaseDialog.vue'
import SelectKnowledgeConfigDialog from './SelectKnowledgeConfigDialog.vue'
import { appsApi } from '@/api/apps'
import KnowledgeConfigSummary from './KnowledgeConfigSummary.vue'


const router = useRouter()
const route = useRoute()
const activeChatId = ref<string>('')
const activeChatTitle = ref<string>('')
const username = ref<string>('')

// 全局只定义一次 username 和 app_id

const appId = route.query.appId as string

const sections = [
  {
    title: '关联知识库',
    btnText: ['+ 选择', '参数'],
    key: 'knowledge'
  },
  {
    title: '工具调用',
    btnText: ['+ 选择', '参数'],
    key: 'tool'
  },
  {
    title: '文件上传',
    btnText: ['关闭'],
    key: 'upload'
  },
  {
    title: '全局变量',
    btnText: ['+ 新增', '导入'],
    key: 'variable'
  }
]

const chatModels = ref<AIModel[]>([])
const chatModel = ref<string>('')
const appName = ref<string>('')
const creatorId = ref<string>('')
const creatorName = ref<string>('')
const appDesc = ref<string>('')
const appIcon = ref<string>('')
const appBg = ref<string>('')
const appTypeId = ref<string>('')
const appTypeName = ref<string>('')
const chatModelParams = ref<ModelParams>()
const appPrivacy = ref<string>('')
const prompt = ref<string>('')
const selectedTool = ref<Tool[]>([])
const selectedFile = ref<FileUpload[]>([])
const selectedKB = ref<KnowledgeBase[]>([])
const knowledgeConfig = ref<any>(null)
const maxTokens = ref<number>(8192)
const maxTemperature = ref<number>(1.0)
const showKbDialog = ref(false)
const showConfigDialog = ref(false)
const fileConfig = ref<any>(null)

onMounted(async () => {
  const userCookie = cookieUtils.getCookie('user');
  if (userCookie) {
    try {
      const userObj = JSON.parse(userCookie);
      username.value = userObj.username;
    } catch (e) {
      console.error('解析user cookie失败', e);
    }
  }
  if(!appId){
    router.push('/workflow/apps')
    return
  }
  try {
    const appDetail = await modelApi.getAppDetail(appId)
    chatModels.value = await modelApi.getModels()
    chatModel.value = appDetail.chatModel
    chatModelParams.value = appDetail.chatModelParams
    appName.value = appDetail.appName
    creatorId.value = appDetail.creatorId
    creatorName.value = appDetail.creatorName
    appDesc.value = appDetail.appDesc
    appIcon.value = appDetail.appIcon
    appBg.value = appDetail.appBg
    appTypeId.value = appDetail.appTypeId
    appTypeName.value = appDetail.appTypeName
    appPrivacy.value = appDetail.appPrivacy
    prompt.value = appDetail.prompt
    selectedTool.value = appDetail.selectedTool
    selectedFile.value = appDetail.selectedFile
    selectedKB.value = appDetail.selectedKB
    knowledgeConfig.value = appDetail.knowledgeSettingsConfig
    fileConfig.value = appDetail.fileSettingsConfig
  } catch (e) {
    // knowledgeBases.value = []
  }
})

function onModelChange(modelName: string) {
  const model = chatModels.value.find(m => m.modelName === modelName)
  if (model) {
    maxTokens.value = model.maxTokens
    maxTemperature.value = model.maxTemperature
  }
}

const promptPlaceholder = `模型固定的引导词，通过调整该内容，可以引导模型聊天方向。该内容会被固定在上下文的开头。可通过输入 / 插入选择变量
如果关联了知识库，你还可以通过适当的描述，来引导模型何时去调用知识库搜索。例如：你是一个AI助手！`

function getAppPayload() {

  const payload = {
    appId: appId,
    appName: appName.value,
    appTypeId: appTypeId.value,
    appTypeName: appTypeName.value,
    creatorId: creatorId.value,
    appIcon: appIcon.value,
    appDesc: appDesc.value,
    chatModel: chatModel.value,
    chatModelParams: chatModelParams.value,
    prompt: prompt.value,
    selectedKB: selectedKB.value || {},
    selectedTool: selectedTool.value || {},
    selectedFile: selectedFile.value || {},
    appPrivacy: appPrivacy.value,
    appStatus: 'published',
  };
  return payload
}

const saveApp = async () => {
  if (!username || !appId) {
    console.error('username 或 appId 不存在')
    return
  }
  const payload = getAppPayload()
  try {
    await modelApi.updateApp(payload)
    // 可加提示
    
    router.push('/workflow/apps')
  } catch (e) {
    console.error('保存失败', e)
  }
}

const goToApps = async () => {
  router.push('/workflow/apps')
}

const goToChat = async () => {
  if (!username || !appId) {
    console.error('username 或 appId 不存在')
    return
  }
  // 先保存到后端
  const payload = getAppPayload()
  try {
    await modelApi.updateApp(payload)
    // 保存成功后再跳转或其他逻辑
    const appitem = {
      appId: appId,
      appName: appName.value,
      appIcon: appIcon.value,
      appBg: appBg.value,
      updatedAt: dayjs().format('YYYY-MM-DDTHH:mm:ss')
    };

    useAppStore().setCurrentApp(appitem)
    router.push({ name: 'ChatContent', query: { appId: appId } })
  } catch (e) {
    console.error('保存失败，无法跳转', e)
  }
}

function handleSectionBtnClick(btn: string, section: any) {
  if (section.key === 'knowledge' && btn === '+ 选择') {
    showKbDialog.value = true
  }
  if (section.key === 'knowledge' && btn === '参数') {
    showConfigDialog.value = true
  }
}

function handleKbSelect(kbs: { kbId: string, kbName: string }[]) {
  const newSelected = kbs.map(kb => ({
    kbId: kb.kbId,
    kbName: kb.kbName
  }))
  // 判断有变化才更新
  const isChanged = JSON.stringify(selectedKB.value) !== JSON.stringify(newSelected)
  if (isChanged) {
    selectedKB.value = newSelected
    appsApi.updateAppKnowledge({
      appId,
      username: username.value,
      selectedKB: newSelected
    })
      .then(() => {
        // 可选：提示保存成功
      })
      .catch(e => {
        // 可选：提示保存失败
        console.error('知识库选择保存失败', e)
      })
  }
}

function handleConfigSelect(config: any) {
  // 判断有变化才更新
  const oldConfig = knowledgeConfig.value
  const isChanged = JSON.stringify(oldConfig) !== JSON.stringify(config)
  if (isChanged) {
    console.log('config', config)
    knowledgeConfig.value = config
    appsApi.updateAppKnowledgeSettings({
      appId,
      username: username.value,
      knowledgeSettingsConfig: config
    })
      .then(() => {
        // 可选：提示保存成功
      })
      .catch(e => {
        // 可选：提示保存失败
        console.error('知识库配置保存失败', e)
      })
  }
}
</script>

<style scoped>
/* 1. 外层背景和主卡片布局 */
.template-detail-bg {
  height: 100%;
  min-height: 100%;
  background: #181a20;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;

}
.main-card {
  height: 100%;
  width: 100%;
  background: #23242a;
  border-radius: 2rem;
  box-shadow: 0 8px 48px #000a;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 2. 顶部 header 和按钮 */
.header {
  width: 100%;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #23242a;
  border-bottom: 1px solid #232c3a;
  padding: 0 32px;
  position: sticky;
  top: 0;
  z-index: 10;
}

.card-icon {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  background: none;
  box-shadow: none;
  margin-right: 0.7rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: none;
  padding: 0;
}
.card-icon i {
  font-size: 1.5rem;
  color: #f0f3f5;
  filter: none;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  color: #e2e8f0;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  padding: 6px 16px;
  border-radius: 8px;
  transition: background 0.18s;
}
.back-btn:hover {
  background: #232c3a;
}
.save-btn {
  background: linear-gradient(90deg, #6a6ff7 0%, #7fdcff 100%);
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  padding: 7px 32px;
  cursor: pointer;
  box-shadow: 0 2px 12px #6a6ff733;
  transition: background 0.2s, box-shadow 0.2s;
}
.save-btn:hover {
  background: linear-gradient(90deg, #7a7fff 0%, #7fdcff 100%);
  box-shadow: 0 4px 18px #6fd0ff55;
}

/* 3. 主内容区 */
.main-content-wrap {
  display: flex;
  flex: 1 1 auto;
  min-height: 0;
}

/* 4. 左侧面板及卡片 */
.left-panel {
  max-width: 670px;
  min-width: 580px;
  background: #23242a;
  padding: 15px 8px 0 15px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  border-right: 1px solid #232c3a;
  overflow-y: auto;
  margin-bottom: 10px;
}
.left-panel::-webkit-scrollbar {
  width: 8px;
  background: #23242a;
}
.left-panel::-webkit-scrollbar-thumb {
  background: #2a2d36;
  border-radius: 8px;
}
.left-panel::-webkit-scrollbar-thumb:hover {
  background: #3a3e48;
}
.app-card {
  background: #2e323a;
  border-radius: 16px;
  padding: 8px 20px;
  
  box-shadow: 0 2px 8px #0006;
}
.app-title {
  display: flex;
  align-items: center;
  font-size: 1.2rem;
  font-weight: bold;
  gap: 0.7rem;
  margin-bottom: 8px;
}
.app-icon {
  color: #7fdcff;
  font-size: 1.6rem;
}
.app-desc {
  color: #b0b8c1;
  font-size: 0.95rem;
  margin-bottom: 30px;
  padding-left: 5px;
}
.app-actions {
  display: flex;
  gap: 30px;
  padding-left: 5px;
}
.action-btn {
  background: #232c3a;
  border: 1.5px solid #2a2a33;
  border-radius: 8px;
  padding: 2px 18px;
  color: #7fdcff;
  font-size: 0.7rem;
  cursor: pointer;
  transition: background 0.2s, border-color 0.2s;
  border-color: #7fdcff;
}
.action-btn:hover {
  background: #232c3a;
  border-color: #7fdcff;
  color: #fff;
}
.section-card {
  background: #2e323a;
  border-radius: 16px;
  box-shadow: 0 2px 8px #0006;
  padding: 10px 16px 10px 16px;
  margin-bottom: 10px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.ai-config {
  display: flex;
  flex-direction: column;
  gap: 12px;
  background: #2e323a;
  padding: 0px 14px;
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1.5px solid #232c3a;
}
.section-title {
  font-weight: bold;
  color: #7fdcff;
  font-size: 1rem;
  margin-bottom: 0;
}
.config-row {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 0;
}
.config-row.horizontal {
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}
.config-row.horizontal span {
  flex-shrink: 0;
  font-size: 0.9rem;
}
.config-row:not(.horizontal) span {
  font-size: 0.85rem;
}
.config-row.horizontal select {
  min-width: 300px;
  background: #181a20;
  color: #e2e8f0;
  border: 1.5px solid #2a2a33;
  border-radius: 6px;
  padding: 4px 10px;
}
.prompt-input {
  height: 120px !important;
  min-height: 120px !important;
  max-height: 120px !important;
  box-sizing: border-box;
  padding: 8px 12px;
  border-radius: 6px;
  border: 1.5px solid #2a2a33;
  background: #181a20;
  color: #e2e8f0;
  font-size: 1rem;
  resize: none;
  line-height: 1.6;
}
.prompt-input:focus {
  border-color: #7fdcff;
  outline: none;
}
.prompt-input::placeholder {
  font-size: 0.7rem;
}
.section-btn {
  background: #232c3a;
  border-radius: 6px;
  padding: 4px 14px;
  color: #7fdcff;
  font-size: 0.92rem;
  cursor: pointer;
  margin-top: 6px;
  transition: background 0.2s, color 0.2s;
}
.section-btn:hover {
  background: #7fdcff;
  color: #232c3a;
}

/* 5. 右侧调试区 */
.right-panel {
  flex: 1;
  background: #23242a;
  padding: 20px 32px 0 32px;
  display: flex;
  min-height: 0;
  overflow: hidden;
  flex-direction: column;
}
.preview-title {
  font-size: 1.2rem;
  font-weight: bold;
  margin-bottom: 20px;
  color: #e2e8f0;
}
.preview-area {
  flex: 1;
  min-height: 0;
  background: #23242a!important;
  border-radius: 12px;
  margin-bottom: 18px;
  border: 1px solid #232c3a;
}
.input-tip {
  color: #b0b8c1;
  font-size: 0.95rem;
  margin-bottom: 12px;
}

.section-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  padding-bottom: 10px;
  border-bottom: 1px solid #232c3a;
}
.section-btn-group {
  display: flex;
  gap: 10px;
}
.section-row.no-border {
  border-bottom: none !important;
}
</style> 