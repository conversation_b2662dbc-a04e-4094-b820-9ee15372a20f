import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface AppItem {
  appId: string;
  appName: string;
  appIcon: string;
  appBg: string;
  updatedAt: string;
}

export const useAppStore = defineStore('app', () => {
  // 原有的状态
  
  // 新增的应用信息状态
  const currentApp = ref<AppItem | null>(null)


  // 新增的应用信息方法
  function setCurrentApp(app: AppItem) {
    currentApp.value = app
  }

  function clearCurrentApp() {
    currentApp.value = null
  }

  const getCurrentApp = computed(() => currentApp.value)

  return {
    currentApp,
    setCurrentApp,
    clearCurrentApp,
    getCurrentApp
  }
}) 