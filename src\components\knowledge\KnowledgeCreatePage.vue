<template>
  <div class="knowledge-page">
    <div class="header-row">
      <h2>我的知识库</h2>
      <div class="header-actions" style="position:relative;">
        <input class="search-input" placeholder="知识库名称" v-model="search" />
        <div
          class="create-btn-wrapper"
          @mouseenter="showTypeSelect = true"
          @mouseleave="showTypeSelect = false"
        >
          <button class="create-btn">+ 新建</button>
          <div class="type-select-container" v-if="showTypeSelect">
            <KnowledgeTypeSelect @select="handleTypeSelect" />
          </div>
        </div>
      </div>
    </div>
    <div class="card-list">
      <div 
        class="knowledge-card" 
        v-for="item in filteredList" 
        :key="item.kbId"
        @click="handleCardClick(item)"
      >
        <div class="card-title-row">
          <div class="card-icon" :style="{background: item.kbBg}">
            <i :class="item.kbIcon"></i>
          </div>
          <span class="card-title">{{ item.kbName }}</span>
          <span class="card-tag">{{ item.kbTypeName }}</span>
        </div>
        <div class="card-desc">{{ item.kbDesc || '这个知识库还没有介绍~' }}</div>
        <div class="card-footer">
          <span><i class="fas fa-user"></i> {{ item.creatorName }}</span>
          <span class="private-tag" v-if="item.kbPrivate">私有</span>
          <span class="public-tag" v-else>公开</span>
          <span class="model-info"><i class="fas fa-brain"></i> {{ item.embeddingModel }}</span>
        </div>
      </div>
    </div>
    <KnowledgeCreateDialog
      v-if="showCreateDialog"
      @close="showCreateDialog = false"
      :kbTypeId="selectedKBTypeId"
      :kbTypeName="selectedKBTypeName"
      :kbIcon="selectedKBIcon"
      :kbBg="selectedKBBg"
    />
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import KnowledgeTypeSelect from './KnowledgeTypeSelect.vue';
import KnowledgeCreateDialog from './KnowledgeCreateDialog.vue';
import { getKnowledgeBaseList, KnowledgeBaseItem } from '@/api/kb';
import { cookieUtils } from '@/utils/cookie';
import { useKnowledgeStore } from '@/store/knowledge';

const router = useRouter();
const knowledgeStore = useKnowledgeStore();

const search = ref('');
const showTypeSelect = ref(false);
const showCreateDialog = ref(false);
const selectedKBTypeId = ref('');
const selectedKBTypeName = ref('');
const selectedKBIcon = ref('');
const selectedKBBg = ref('');
const knowledgeList = ref<KnowledgeBaseItem[]>([]);

onMounted(async () => {
  let username = '';
  const userCookie = cookieUtils.getCookie('user');
  if (userCookie) {
    try {
      const userObj = JSON.parse(userCookie);
      username = userObj.username || '';
    } catch (e) {
      username = '';
    }
  }
  if (username) {
    try {
      const res = await getKnowledgeBaseList(username);
      console.log(res);
      knowledgeList.value = res.items;
    } catch (e) {
      knowledgeList.value = [];
    }
  }
});

const filteredList = computed(() =>
  knowledgeList.value.filter(item => item.kbName.includes(search.value))
);

const handleTypeSelect = (kbTypeId: string, kbTypeName: string, kbIcon: string, kbBg: string) => {
  showTypeSelect.value = false;
  showCreateDialog.value = true;
  selectedKBTypeId.value = kbTypeId;
  selectedKBTypeName.value = kbTypeName;
  selectedKBIcon.value = kbIcon;
  selectedKBBg.value = kbBg;
};

const handleCardClick = (item: KnowledgeBaseItem) => {
  knowledgeStore.setDatasetInfo({
    kbName: item.kbName,
    kbTypeId: item.kbTypeId,
    kbTypeName: item.kbTypeName,
    kbIcon: item.kbIcon,
    kbBg: item.kbBg,
    embeddingModel: item.embeddingModel,
    chatModel: item.chatModel,
    imageModel: item.imageModel,
    kbDesc: item.kbDesc,
    accessLevel: item.accessLevel,
  });
  router.push({ 
    path: '/dataset/detail', 
    query: { 
      kbId: item.kbId 
    } 
  });
};
</script>

<style scoped>
.knowledge-page {
  padding: 12px 24px;
  background: #1a1a1a;
  min-height: 100vh;
}
.header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  margin-top: 10px;
}
.header-row h2 {
  font-size: 1.5rem;
  font-weight: bold;
  color: #e6eaf3;
}
.header-actions {
  display: flex;
  gap: 16px;
  align-items: center;
}
.search-input {
  padding: 8px 16px;
  border: 1px solid #23242a;
  border-radius: 8px;
  font-size: 1rem;
  background: #23242a;
  color: #e6eaf3;
}
.create-btn {
  background: linear-gradient(90deg, #5b8cff 0%, #7fdcff 100%);
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 8px 24px;
  font-size: 1rem;
  cursor: pointer;
  font-weight: bold;
  transition: background 0.2s;
}
.create-btn:hover {
  background: linear-gradient(90deg, #6a9cff 0%, #8fecff 100%);
}
.card-list {
  display: flex;
  flex-wrap: wrap;
  gap: 32px;
}
.knowledge-card {
  background: #23242a;
  border-radius: 18px;
  box-shadow: 0 2px 12px #000a;
  padding: 24px 10px;
  width: 410px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  border: 1px solid #2d3651;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}
.knowledge-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
.card-title-row {
  display: flex;
  align-items: center;
  gap: 12px;
}

.card-icon  {
  width: 35px;
  height: 35px;
  border-radius: 12px;
  background: #e6eaf3;
  display: flex;
  align-items: center;
  justify-content: center;
}
.card-icon  i {
  font-size: 20px;
  color: rgb(233, 235, 240);
}
.card-title {
  font-size: 1.1rem;
  font-weight: bold;
  color: #e6eaf3;
}
.card-tag {
  background: #2d3651;
  color: #5b8cff;
  border-radius: 8px;
  padding: 2px 10px;
  font-size: 0.7rem;
  margin-left: auto;
}
.card-desc {
  color: #8b95a5;
  font-size: 0.7rem;
  min-height: 32px;
}
.card-footer {
  display: flex;
  align-items: center;
  gap: 18px;
  color: #8b95a5;
  font-size: 0.95rem;
  position: relative;
}
.private-tag {
  background: #1a1a2a;
  color: #5b8cff;
  border-radius: 6px;
  padding: 2px 8px;
  font-size: 0.85rem;
}
.public-tag {
  background: #1a2a1a;
  color: #2ecc40;
  border-radius: 6px;
  padding: 2px 8px;
  font-size: 0.85rem;
}
.model-info {
  margin-left: auto;
  display: flex;
  align-items: center;
  color: #8b95a5;
}
.model-info i { 
  margin-right: 4px;
  color: #137aaa;
}
.create-btn-wrapper {
  display: inline-block;
  position: relative;
  padding: 10px;
  margin: -10px;
}
.type-select-container {
  position: absolute;
  top: 100%;
  right: 0;
}
</style>