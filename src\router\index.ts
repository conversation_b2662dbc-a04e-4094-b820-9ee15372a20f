import { createRouter, createWebHistory } from 'vue-router'
import LoginPage from '@/views/LoginPage.vue'
import Home from '@/views/Home.vue'
import ChatPage from '@/components/chat/ChatPage.vue'
import ChatContent from '@/components/chat/ChatContent.vue'
import WorkflowCreatePage from '@/components/workflow/WorkflowCreatePage.vue'
import KnowledgeCreatePage from '@/components/knowledge/KnowledgeCreatePage.vue'
import TemplateDetail from '@/components/workflow/TemplateDetail.vue'
import KnowledgeDetailPage from '@/components/knowledge/KnowledgeDetailPage.vue'
import ImportUploadPage from '@/components/knowledge/ImportUploadPage.vue'
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: Home,
      children: [
        {
          path: 'workflow/apps',
          name: 'WorkflowCreatePage',
          component: WorkflowCreatePage
        },
        {
          path: 'dataset/list',
          name: 'KnowledgeCreatePage',
          component: KnowledgeCreatePage
        },
        {
          path: 'app/detail',
          name: 'TemplateDetail',
          component: TemplateDetail
        },
        {
          path: 'dataset/detail',
          name: 'KnowledgeDetailPage',
          component: KnowledgeDetailPage
        },
        {
          path: 'dataset/detail',
          name: 'ImportUploadPage',
          component: ImportUploadPage
        }
      ]
    },
    {
      path: '/login',
      name: 'login',
      component: LoginPage
    },
    {
      path: '/chat',
      name: 'ChatPage',
      component: ChatPage,
      children: [
        {
          path: '/chat',
          name: 'ChatContent',
          component: ChatContent
        }
      ]
    }
  ]
})

export default router 