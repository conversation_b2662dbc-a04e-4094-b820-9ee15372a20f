<template>
  <div class="dropdown-menu">
    <div class="dropdown-item" v-for="item in items" :key="item.appTypeId" @click="handleSelect(item)">
      <div class="item-icon" :style="{background: item.appTypeBg}">
        <i :class="item.appTypeIcon"></i>
      </div>
      <div class="item-content">
        <div class="item-title">{{ item.appTypeName }}</div>
        <div class="item-desc">{{ item.appTypeDesc }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineEmits, ref, onMounted } from 'vue';
import { appsApi, AppTypeItem } from '@/api/apps';

const emit = defineEmits(['select']);

const items = ref<AppTypeItem[]>([]);

onMounted(async () => {
  try {
    items.value = await appsApi.getAppTypes();
  } catch (e) {
    items.value = [];
    console.error('获取应用类型失败', e);
  }
});

function handleSelect(item: AppTypeItem) {
  emit('select', { appTypeId: item.appTypeId, appTypeName: item.appTypeName, appTypeIcon: item.appTypeIcon, appTypeBg: item.appTypeBg });
}

</script>

<style scoped>
.dropdown-menu {
  width: 280px;
  min-width: 280px;
  max-width: 280px;
  background: #23242a;
  border-radius: 1.2rem;
  box-shadow: 0 4px 32px #000a, 0 1.5px 8px #4fc3ff11 inset;
  padding: 0.2rem 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  z-index: 1000;
}

.dropdown-item {
  display: flex;
  align-items: flex-start;
  gap: 0.8rem;
  padding: 0.5rem 0.7rem;
  border-radius: 0.9rem;
  cursor: pointer;
  transition: background 0.18s, box-shadow 0.18s;
  background: none;
}
.dropdown-item:hover {
  background: linear-gradient(90deg, #818388 0%, #72768b 100%);
  box-shadow: 0 2px 12px #4fc3ff44;
  color: #fff;
}
.dropdown-item:hover .item-title {
  color: #4fc3ff;
}
.item-icon {
  width: 40px;
  height: 40px;
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 1.7rem;
  flex-shrink: 0;
  box-shadow: 0 2px 12px #4fc3ff33;
  background: linear-gradient(135deg, #232c3a 60%, #1a1a1a 100%);
}
.item-content {
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
}
.item-title {
  font-size: 0.9rem;
  font-weight: bold;
  color: #fff;
}
.item-desc {
  font-size: 0.7rem;
  color: #b0b8c1;
  line-height: 1.5;
}
</style> 