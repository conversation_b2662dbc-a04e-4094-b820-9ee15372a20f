<template>
  <div class="chat-card">
    <ChatSidebar 
      ref="sidebarRef"
      :app-id="appId"
      :actives="actives"
      @select="handleChatSelect"
      @new="handleNewChat"
    />
    <ChatMain
      :app-id="appId"
      :active-chat-id="activeChatId"
      :active-chat-title="activeChatTitle"
      @new-chat="handleNewChatCreated"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import ChatSidebar from './ChatSidebar.vue';
import ChatMain from './ChatMain.vue';
import type { ChatInfoItem } from '@/api/chat';
import { chatApi } from '@/api/chat';



const route = useRoute();
const activeChatId = ref('');
const actives = ref('');
const activeChatTitle = ref('');
const sidebarRef = ref();
const router = useRouter();

// 从query参数获取应用ID
const appId = ref<string>('');

onMounted(async () => { 
  appId.value = route.query.appId as string;
  if (!appId.value) {
    router.push('/workflow/apps')
    return
  }
  try {
    const res = await chatApi.validateApp(appId.value);
    console.log('res', res)
    if (!res.exists) {
      router.push('/workflow/apps')
      return
    }
  } catch (error) {
    console.error('验证应用ID失败:', error);
    router.push('/workflow/apps')
  }
});


// 处理选择对话
const handleChatSelect = (chatId: string, chatTitle: string) => {
  actives.value = chatId
  activeChatId.value = chatId;
  activeChatTitle.value = chatTitle;

  // TODO: 实现切换对话的逻辑
};

// 处理新建对话
const handleNewChat = (newChat: ChatInfoItem) => {
    actives.value = ''
    activeChatId.value = newChat.chatId;
    activeChatTitle.value = newChat.chatTitle;
};

// 处理新对话创建
const handleNewChatCreated = (newChatInfo: ChatInfoItem) => {
  
  // 更新侧边栏的对话列表
  if (sidebarRef.value) {
    sidebarRef.value.addConversation({
      chatId: newChatInfo.chatId,
      chatTitle: newChatInfo.chatTitle,
      updatedAt: newChatInfo.updatedAt
    });
    actives.value = newChatInfo.chatId
  }
  
};

</script>

<style scoped>
.chat-card {
  flex: 1;
  display: flex;
  margin: 20px;
  background-color: #23242a;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  border: 1px solid #2d3651;
  overflow: hidden;
}
</style> 