<template>
  <div class="import-upload-bg">
    <div class="import-upload-page">
      <div class="card-header">
        <span class="back-btn" @click="handleHeaderBack">
          <i class="fas fa-arrow-left"></i>
          {{ [1,2,3].includes(currentStep) ? '上一步' : '退出' }}
        </span>
      </div>
      <div class="steps">
        <div v-for="(step, idx) in steps" :key="step" class="step-wrap">
          <div :class="['step', {active: currentStep >= idx}]">
            <span class="step-index">{{ idx + 1 }}</span>
            <span class="step-label">{{ step }}</span>
          </div>
          <span
            v-if="idx < steps.length - 1"
            :class="['step-divider', {active: currentStep > idx}]"
          ></span>
        </div>
      </div>
      <div class="content-container">
        <StepOneFileUpload v-if="currentStep === 0" v-model:files="files" @next="handleFileUploadNext" />
        <StepTwoFileSettings v-if="currentStep === 1" v-model:settings="settings" @next="handleSettingsNext" />
        <StepThreePreview v-if="currentStep === 2" :files="files" @next="() => currentStep = 3" />
        <StepFourComfirmUpload v-if="currentStep === 3" :files="files" @update:files="val => files = val" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import StepOneFileUpload from './StepOneFileUpload.vue'
import StepTwoFileSettings from './StepTwoFileSettings.vue'
import StepThreePreview from './StepThreePreview.vue'
import StepFourComfirmUpload from './StepFourComfirmUpload.vue'
import { importSettings } from '@/api/kb'

const router = useRouter()
const route = useRoute()
const steps = ['选择文件', '参数设置', '数据预览', '确认上传']
const currentStep = ref(0)
const files = ref<Array<{
  id: string,
  file: File,
  name: string,
  size: number,
  progress?: number,
  error?: string
}>>([])
const settings = ref<any>(null)

function handleFileUploadNext(uploadedFiles: typeof files.value) {
  files.value = uploadedFiles
  currentStep.value = 1
}

async function handleSettingsNext(newSettings: any) {
  settings.value = newSettings
  currentStep.value = 2
  
  try {
    const kbId = route.query.kbId as string
    if (!kbId) {
      throw new Error('缺少知识库ID')
    }

    // 获取所有已上传文件的ID
    const fileIds = files.value
      .filter(file => file.progress === 100 && !file.error)
      .map(file => file.id)

    if (fileIds.length === 0) {
      throw new Error('没有可用的文件')
    }
    console.log(fileIds, newSettings)
    // 发送设置和文件ID到后端
    await importSettings({
      kbId,
      fileIds,
      settings: newSettings
    })

    // 处理成功响应
    console.log('设置已保存')
  } catch (error) {
    console.error('保存设置失败:', error)
    // 这里可以添加错误提示UI
  }
}

function handleHeaderBack() {
  if (currentStep.value === 1) {
    currentStep.value = 0
  } else if (currentStep.value === 2) {
    currentStep.value = 1
  } else if (currentStep.value === 3) {
    currentStep.value = 2
  } else {
    router.back()
  }
}

</script>

<style scoped>
.import-upload-bg {
  min-height: 100%;
  height: 100%;
  background: #18191c;
  align-items: flex-start;
  justify-content: center;
  padding: 10px;
}

.import-upload-page {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #1e1f23;
}

.steps-container {
  padding: 24px 48px;
  border-bottom: 1px solid #2a2b2f;
}

.content-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.content-container > div {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-header {
  display: flex;
  align-items: center;
  padding: 12px 48px 0 48px;
  min-height: 60px;
  order: -2;
}

.back-btn {
  color: #8b95a5;
  font-size: 1.1rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: color 0.3s ease;
}

.back-btn:hover {
  color: #f5f6fa;
}

.steps {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 48px;
  margin-bottom: 18px;
  order: -1;
}

.step-wrap {
  display: flex;
  align-items: center;
}

.step {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #8b95a5;
  font-size: 1.1rem;
  font-weight: 500;
}

.step.active {
  color: #56dfdf;
}

.step.active .step-index {
  background: #54bdbd;
  color: #fff;
}

.step-index {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: #2a2b2f;
  color: #8b95a5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.1rem;
}

.step-divider {
  width: 80px;
  height: 2px;
  background: #2a2b2f;
  margin: 0 12px;
  border-radius: 1px;
  transition: background 0.3s;
}

.step-divider.active {
  background: #54bdbd;
}
</style>
